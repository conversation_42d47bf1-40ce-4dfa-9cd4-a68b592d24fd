# Authentication System Fixes

## Problem Summary

The coupon microservice system was experiencing authentication issues where:

1. **Registration endpoint** (`/api/register`) was incorrectly requiring authentication
2. **gRPC service calls** from API Gateway were failing with "missing authorization header" errors
3. **JWT middleware** was only looking for Authorization headers instead of HTTP-only cookies
4. **Service-to-service authentication** was not properly configured for API Gateway clients

## Root Cause Analysis

### 1. **Missing Service Credentials in API Gateway**
- API Gateway clients (`UserClient`, `AuthClient`) were not setting `client-id` and `client-key` metadata for gRPC calls
- This caused the backend services to reject the calls with "missing authorization header" errors
- Other services (auth-service, user-service) were properly setting service credentials

### 2. **Incorrect Authentication Middleware Configuration**
- Cookie authentication middleware was not skipping public endpoints like `/api/register`
- Skip paths were configured as `["/api/auth/", "/api/health", "/api/metrics"]` but registration was at `/api/register`

### 3. **JWT Library Missing Cookie Support**
- Shared JWT library only supported Authorization header extraction
- No method to extract tokens from HTTP-only cookies

## Solutions Implemented

### 1. **Added Service Credentials to API Gateway Clients**

**Updated UserClient (`coupon-api-gateway/internal/clients/user_client.go`):**
```go
type UserClient struct {
    Client    user_proto_v1.UserServiceClient
    conn      *shared_grpc.Client
    clientID  string  // Added
    clientKey string  // Added
}

func NewUserClient(target string, cfg *config.GRPCConfig, logger *logging.Logger, metrics *metrics.Metrics, clientID, clientKey string) (*UserClient, error) {
    // Constructor now accepts service credentials
}

func (c *UserClient) CreateUser(ctx context.Context, name, email, password string) (*user_proto_v1.CreateUserResponse, error) {
    // Add service credentials to metadata
    md := metadata.New(map[string]string{
        "client-id":  c.clientID,
        "client-key": c.clientKey,
    })
    ctx = metadata.NewOutgoingContext(ctx, md)
    
    // Add timeout
    ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
    defer cancel()
    
    return c.Client.CreateUser(ctx, req)
}
```

**Updated AuthClient (`coupon-api-gateway/internal/clients/auth_client.go`):**
- Similar changes to include service credentials in all gRPC calls

### 2. **Fixed Authentication Middleware Skip Paths**

**Updated API Gateway main.go:**
```go
cookieAuthConfig := &custom_middleware.CookieAuthConfig{
    AuthClient:   authClient,
    JWTManager:   jwtManager,
    CookieConfig: cookieConfig,
    Logger:       logger,
    SkipPaths:    []string{"/api/register", "/api/login", "/api/logout", "/api/health", "/api/metrics"}, // Fixed
}
```

**Before:** `["/api/auth/", "/api/health", "/api/metrics"]`
**After:** `["/api/register", "/api/login", "/api/logout", "/api/health", "/api/metrics"]`

### 3. **Enhanced JWT Library with Cookie Support**

**Added to `coupon-shared-libs/auth/jwt.go`:**
```go
// ExtractTokenFromCookie extracts JWT token from HTTP cookies
func (j *JWTManager) ExtractTokenFromCookie(r *http.Request, cookieName string) (string, error) {
    cookie, err := r.Cookie(cookieName)
    if err != nil {
        return "", errors.New("missing authentication cookie")
    }
    
    if cookie.Value == "" {
        return "", errors.New("empty authentication cookie")
    }
    
    return cookie.Value, nil
}
```

### 4. **Updated Service Credential Configuration**

**API Gateway now passes service credentials to clients:**
```go
authClient, err := clients.NewAuthClient(cfg.DownstreamServices.AuthServiceAddr, &cfg.GRPC, logger, appMetrics, cfg.Service.ClientID, cfg.Service.ClientKey)

userClient, err := clients.NewUserClient(cfg.DownstreamServices.UserServiceAddr, &cfg.GRPC, logger, appMetrics, cfg.Service.ClientID, cfg.Service.ClientKey)
```

## Authentication Flow

### 1. **Registration Flow (Public)**
```
Client -> API Gateway (/api/register) -> User Service (CreateUser) -> Auth Service (Login)
                                      \-> Auth Service (CreateUserCredentialsWithRole)
```
- **No authentication required** for the HTTP request
- **Service credentials** used for gRPC calls between services
- **Cookies set** in response after successful registration

### 2. **Login Flow (Public)**
```
Client -> API Gateway (/api/login) -> Auth Service (Login)
                                   \-> User Service (GetUserByEmail)
```
- **No authentication required** for the HTTP request
- **Service credentials** used for gRPC calls
- **Cookies set** in response after successful login

### 3. **Protected Endpoints**
```
Client -> API Gateway (with cookies) -> Cookie Auth Middleware -> Protected Handler -> Backend Services
```
- **Cookie authentication** required for HTTP request
- **Service credentials** used for gRPC calls to backend services

## Service-to-Service Authentication

### **API Gateway Configuration:**
```yaml
service:
  client_id: "api-gateway-client"
  client_key: "${API_GATEWAY_KEY}"
```

### **gRPC Metadata:**
```go
md := metadata.New(map[string]string{
    "client-id":  c.clientID,
    "client-key": c.clientKey,
})
ctx = metadata.NewOutgoingContext(ctx, md)
```

### **Backend Service Validation:**
- Auth Service validates service credentials
- User Service allows service-to-service calls with valid credentials
- Public methods (like CreateUser) don't require authentication

## Testing the Fixes

### 1. **Test Registration (Should Work)**
```bash
curl -X POST http://localhost:8083/api/register \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test User",
    "email": "<EMAIL>", 
    "password": "password123"
  }'
```

### 2. **Test Login (Should Work)**
```bash
curl -X POST http://localhost:8083/api/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

### 3. **Test Protected Endpoint (Should Work with Cookies)**
```bash
curl -X GET http://localhost:8083/api/users/me \
  -H "Cookie: access_token=<token_from_login>"
```

## Key Benefits

1. **✅ Registration Works**: No authentication required for public endpoints
2. **✅ Service Communication**: Proper service-to-service authentication
3. **✅ Cookie-Based Auth**: HTTP-only cookies for better security
4. **✅ Consistent Architecture**: All services follow the same authentication pattern
5. **✅ Proper Error Handling**: Clear error messages for authentication failures

## Environment Variables Required

Make sure these environment variables are set:

```bash
# API Gateway
API_GATEWAY_KEY=your-api-gateway-secret-key

# JWT Secret (shared across services)
JWT_SECRET_KEY=your-jwt-secret-key
```

The authentication system now properly supports both cookie-based user authentication and service-to-service authentication with proper credential validation.
