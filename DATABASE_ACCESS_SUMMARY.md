# Database Access Summary

## 🎯 **Quick Access Guide**

### **Shared Database Studio (Adminer) - Web Interface**

**Single URL**: http://localhost:8080

| Database | Server Name | Connection Details |
|----------|-------------|-------------------|
| **Auth Service** | `postgres-auth` | User: `coupon`<br>Password: `coupon`<br>Database: `auth_db` |
| **User Service** | `postgres-user` | User: `coupon`<br>Password: `coupon`<br>Database: `user_db` |

*Note: Use the same web interface but different server names to connect to different databases.*

### **Direct Database Access**

| Service | PostgreSQL Port | Connection String |
|---------|----------------|-------------------|
| **Auth Service** | `localhost:5433` | `postgresql://coupon:coupon@localhost:5433/auth_db` |
| **User Service** | `localhost:5434` | `postgresql://coupon:coupon@localhost:5434/user_db` |

## 🚀 **Quick Start**

### **Option 1: Use Convenience Script**
```bash
./open-database-studio.sh
```
This will:
- Start both services if not running
- Open both database studios in your browser
- Display connection information

### **Option 2: Manual Access**
```bash
# Start services
cd coupon-auth-service && docker-compose up -d
cd ../coupon-user-service && docker-compose up -d

# Access shared database studio
# Single URL: http://localhost:8080
# Switch between databases using different server names
```

## 📊 **Database Schemas**

### **Auth Service Database (`auth_db`)**
Tables created by GORM AutoMigrate:
- `user_credentials` - User authentication data
- `service_credentials` - Service-to-service authentication
- `refresh_tokens` - JWT refresh tokens
- `roles` - User roles (ADMIN, USER)
- `user_roles` - User-role assignments (many-to-many)

### **User Service Database (`user_db`)**
Tables created by GORM AutoMigrate:
- `users` - User profile data with type (NEW, VIP)

## 💡 **Sample Queries**

### **Auth Service Queries**
```sql
-- View all roles (should see ADMIN and USER)
SELECT * FROM roles;

-- View user credentials
SELECT user_id, created_at FROM user_credentials;

-- View user-role assignments
SELECT ur.user_id, r.name as role_name
FROM user_roles ur
JOIN roles r ON ur.role_id = r.id;

-- View service credentials
SELECT name, client_id, version FROM service_credentials;

-- View refresh tokens
SELECT user_id, expires_at FROM refresh_tokens WHERE expires_at > NOW();
```

### **User Service Queries**
```sql
-- View all users
SELECT * FROM users;

-- Count users by type
SELECT type, COUNT(*) as count 
FROM users 
GROUP BY type;

-- View VIP users
SELECT name, email, created_at 
FROM users 
WHERE type = 'VIP';

-- Recent users
SELECT name, email, created_at 
FROM users 
ORDER BY created_at DESC 
LIMIT 10;
```

## 🔧 **Database Management Features**

### **Available in Adminer:**
- ✅ **Browse Data** - View all tables and records
- ✅ **Execute Queries** - SQL editor with syntax highlighting
- ✅ **Edit Records** - Insert, update, delete through web interface
- ✅ **Export Data** - Download data in various formats
- ✅ **Import Data** - Upload CSV, SQL files
- ✅ **Schema Visualization** - View table structure and relationships
- ✅ **Dark Theme** - Easy on the eyes
- ✅ **Mobile Responsive** - Works on phones/tablets

### **Adminer Advantages:**
- 🚀 **Lightweight** - Single container, fast loading
- 🔒 **Secure** - No permanent data storage in container
- 🌐 **Web-based** - No client installation needed
- 📱 **Cross-platform** - Works on any device with browser
- 🎨 **Customizable** - Multiple themes available

## 🛠️ **Troubleshooting**

### **Cannot Access Database Studio:**
1. Check if containers are running: `docker ps | grep adminer`
2. Verify ports are not blocked: `netstat -an | grep 8080`
3. Restart containers: `docker restart adminer adminer-user`

### **Cannot Connect to Database:**
1. Ensure database containers are healthy: `docker ps`
2. Check database logs: `docker logs postgres-auth`
3. Verify network connectivity: `docker network inspect coupon-network`

### **Database Empty:**
1. Check if GORM AutoMigrate ran: `docker logs auth-service`
2. Look for migration success messages in service logs
3. Verify models are properly defined in code

## 🔄 **Development Workflow**

### **Typical Development Process:**
1. **Start Services**: `./open-database-studio.sh`
2. **View Schema**: Check tables were created correctly
3. **Test APIs**: Use Postman/curl to create data
4. **Verify Data**: Use Adminer to see data was inserted
5. **Debug Issues**: Use SQL queries to investigate problems
6. **Export Data**: Backup test data for sharing

### **Data Management:**
- **Development**: Use Adminer for quick data inspection
- **Testing**: Export/import test datasets
- **Debugging**: Execute diagnostic queries
- **Schema Changes**: Verify GORM migrations worked correctly

## 📚 **Additional Resources**

- **Adminer Documentation**: https://www.adminer.org/
- **PostgreSQL Documentation**: https://www.postgresql.org/docs/
- **GORM Documentation**: https://gorm.io/docs/
- **Docker Compose Reference**: https://docs.docker.com/compose/

## 🎯 **Pro Tips**

1. **Bookmark URLs**: Save http://localhost:8080 and http://localhost:8090
2. **Use Favorites**: Save frequently used queries in Adminer
3. **Export Schema**: Regularly backup your database schema
4. **Monitor Performance**: Use EXPLAIN for slow queries
5. **Security**: Never use these credentials in production

Happy database exploring! 🚀
