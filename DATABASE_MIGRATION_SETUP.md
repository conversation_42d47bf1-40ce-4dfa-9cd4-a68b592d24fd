# Database Migration Setup

## Problem Fixed

The services were not applying database migrations on startup, leaving the databases empty. This has been resolved by:

1. **Created Migration Runner**: Added `coupon-shared-libs/database/migrations.go` with automatic migration execution
2. **Updated Services**: Both auth and user services now run migrations on startup
3. **Migration Tracking**: Uses `schema_migrations` table to track executed migrations

## How It Works

### Migration Runner Features:
- **Automatic Execution**: Runs all `.sql` files in the `migrations/` directory on service startup
- **Idempotent**: Tracks executed migrations to avoid re-running them
- **Transactional**: Each migration runs in a transaction for safety
- **Ordered Execution**: Migrations are executed in alphabetical order (001_, 002_, etc.)

### Migration Files:

**Auth Service (`coupon-auth-service/migrations/`):**
- `001_initial_schema.sql` - Creates user_credentials, service_credentials, roles, user_roles, refresh_tokens tables
- `002_remove_email_duplication.sql` - Removes email column from user_credentials

**User Service (`coupon-user-service/migrations/`):**
- `001_initial_schema.sql` - Creates users table
- `002_add_user_type.sql` - Adds user_type ENUM (NEW, VIP) to users table

## Code Changes Made

### 1. Created Migration Runner (`coupon-shared-libs/database/migrations.go`)
```go
type MigrationRunner struct {
    db     *DB
    logger *logging.Logger
}

func (mr *MigrationRunner) RunMigrations(migrationsDir string) error
```

### 2. Updated Auth Service (`coupon-auth-service/cmd/server/main.go`)
```go
// Run database migrations
migrationRunner := database.NewMigrationRunner(db, logger)
if err := migrationRunner.RunMigrations("./migrations"); err != nil {
    logger.Fatalf("failed to run database migrations: %v", err)
}
```

### 3. Updated User Service (`coupon-user-service/cmd/server/main.go`)
```go
// Run database migrations
migrationRunner := database.NewMigrationRunner(db, logger)
if err := migrationRunner.RunMigrations("./migrations"); err != nil {
    logger.Fatalf("failed to run database migrations: %v", err)
}
```

## Testing the Setup

After rebuilding and starting the services, you should see migration logs:

```bash
# Rebuild and start services
cd coupon-auth-service && docker-compose up --build -d
cd ../coupon-user-service && docker-compose up --build -d
cd ../coupon-api-gateway && docker-compose up --build -d
```

### Expected Log Output:
```
INFO Starting database migrations from directory: ./migrations
INFO Executing migration: 001_initial_schema.sql
INFO Migration 001_initial_schema.sql executed successfully
INFO Executing migration: 002_remove_email_duplication.sql
INFO Migration 002_remove_email_duplication.sql executed successfully
INFO Database migrations completed successfully
```

### Verify Database Schema:

**Auth Service Database (port 5433):**
```bash
docker exec -it postgres-auth psql -U coupon -d auth_db -c "\dt"
```

**User Service Database (port 5434):**
```bash
docker exec -it postgres-user psql -U coupon -d user_db -c "\dt"
```

You should see tables like:
- Auth DB: `user_credentials`, `service_credentials`, `roles`, `user_roles`, `refresh_tokens`, `schema_migrations`
- User DB: `users`, `schema_migrations`

## Migration Tracking

The system creates a `schema_migrations` table in each database:
```sql
CREATE TABLE schema_migrations (
    id SERIAL PRIMARY KEY,
    filename VARCHAR(255) NOT NULL UNIQUE,
    executed_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
```

This ensures migrations are only run once and provides an audit trail.

## Adding New Migrations

To add new migrations:

1. Create a new `.sql` file with incremental naming (e.g., `003_add_new_table.sql`)
2. Place it in the appropriate service's `migrations/` directory
3. Restart the service - the migration will run automatically

## Benefits

- ✅ **Automatic Schema Setup**: Databases are properly initialized on first run
- ✅ **Version Control**: Migration files are tracked in git
- ✅ **Idempotent**: Safe to restart services without duplicate migrations
- ✅ **Transactional**: Failed migrations are rolled back
- ✅ **Audit Trail**: Track when migrations were executed
- ✅ **Development Friendly**: New team members get proper database schema automatically
