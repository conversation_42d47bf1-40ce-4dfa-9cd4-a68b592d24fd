# Shared Database Studio Setup - Adminer

## Overview

Added **Shared Adminer** as a single web-based database management tool (similar to Prisma Studio) for easy visualization and management of all PostgreSQL databases across services.

## 🎯 **What is <PERSON>miner?**

Adminer is a lightweight, web-based database management tool that provides:
- **Visual Database Browser** - Browse tables, data, and schema
- **Query Editor** - Execute SQL queries with syntax highlighting
- **Data Editing** - Insert, update, delete records through web interface
- **Schema Visualization** - View table relationships and structure
- **Multi-Database Support** - PostgreSQL, MySQL, SQLite, etc.
- **Single PHP File** - Lightweight and fast

## 🚀 **Access Information:**

### **Shared Adminer Web Interface:**
- **Single URL**: http://localhost:8080
- **Access All Databases**: Switch between databases using different server names
- **Default Theme**: Dark theme (pepa-linha-dark)

### **Database Connections:**

| Database | Server | Username | Password | Database Name |
|----------|--------|----------|----------|---------------|
| Auth Service | `postgres-auth` | `coupon` | `coupon` | `auth_db` |
| User Service | `postgres-user` | `coupon` | `coupon` | `user_db` |

## 📋 **Setup Instructions:**

### 1. **Start Services with Database Studio:**
```bash
# Start auth service (includes Adminer)
cd coupon-auth-service && docker-compose up -d

# Start user service
cd ../coupon-user-service && docker-compose up -d

# Start API gateway
cd ../coupon-api-gateway && docker-compose up -d
```

### 2. **Access Shared Adminer:**
1. Open browser and go to: **http://localhost:8080**
2. You'll see the Adminer login screen
3. Use different server names to connect to different databases

### 3. **Connect to Auth Service Database:**
- **System**: `PostgreSQL`
- **Server**: `postgres-auth`
- **Username**: `coupon`
- **Password**: `coupon`
- **Database**: `auth_db`

### 4. **Connect to User Service Database:**
- **System**: `PostgreSQL`
- **Server**: `postgres-user`
- **Username**: `coupon`
- **Password**: `coupon`
- **Database**: `user_db`

### 5. **Switching Between Databases:**
1. **Login to First Database**: Use one of the connections above
2. **To Switch**: Click "Logout" in the top-right corner
3. **Login to Different Database**: Enter different server name (postgres-auth or postgres-user)
4. **Quick Switch**: You can also bookmark both connection URLs for faster access

## 🔍 **Features Available:**

### **1. Browse Tables:**
- View all tables in each database
- See table structure (columns, types, constraints)
- Browse table data with pagination

### **2. Execute Queries:**
- SQL query editor with syntax highlighting
- Execute SELECT, INSERT, UPDATE, DELETE queries
- View query results in tabular format

### **3. Edit Data:**
- Click on any table to view/edit data
- Add new records through web form
- Edit existing records inline
- Delete records with confirmation

### **4. Schema Management:**
- View table relationships
- See indexes and constraints
- Export database schema
- Import/Export data

## 📊 **Example Queries to Try:**

### **Auth Service Database:**
```sql
-- View all users with credentials
SELECT uc.user_id, uc.created_at 
FROM user_credentials uc;

-- View user roles
SELECT ur.user_id, r.name as role_name
FROM user_roles ur
JOIN roles r ON ur.role_id = r.id;

-- View all roles
SELECT * FROM roles;

-- View service credentials
SELECT name, client_id, version FROM service_credentials;
```

### **User Service Database:**
```sql
-- View all users
SELECT * FROM users;

-- View users by type
SELECT name, email, type FROM users WHERE type = 'VIP';

-- Count users by type
SELECT type, COUNT(*) as count 
FROM users 
GROUP BY type;
```

## 🎨 **Adminer Themes:**

The setup uses the dark theme (`pepa-linha-dark`). Available themes:
- `pepa-linha` (default)
- `pepa-linha-dark` (dark theme)
- `lucas-sandery`
- `pokorny`
- `hydra`

To change theme, modify the docker-compose.yml:
```yaml
environment:
  ADMINER_DESIGN: pepa-linha  # Change to desired theme
```

## 🔧 **Advanced Configuration:**

### **Custom Adminer Plugins:**
You can extend Adminer with plugins by creating a custom image:

```dockerfile
FROM adminer:4.8.1
ADD plugins-enabled/ /var/www/html/plugins-enabled/
```

### **Security Considerations:**
For production, consider:
- Adding authentication
- Restricting network access
- Using HTTPS
- Setting up proper firewall rules

## 🆚 **Adminer vs Prisma Studio:**

| Feature | Adminer | Prisma Studio |
|---------|---------|---------------|
| **Database Support** | Multi-DB (PostgreSQL, MySQL, etc.) | Prisma schema only |
| **Setup** | Docker container | npm package |
| **Query Editor** | Full SQL support | Limited |
| **Data Editing** | Full CRUD operations | Full CRUD operations |
| **Schema Visualization** | Basic | Advanced |
| **Performance** | Lightweight | Heavier |
| **Learning Curve** | Minimal | Minimal |

## 🚨 **Troubleshooting:**

### **Cannot Connect to Database:**
1. Ensure services are running: `docker ps`
2. Check network connectivity: `docker network ls`
3. Verify database credentials in `.env` files

### **Adminer Not Loading:**
1. Check if port 8080 is available
2. Restart Adminer container: `docker restart adminer`
3. Check logs: `docker logs adminer`

### **Database Not Found:**
1. Wait for database to be ready (check health checks)
2. Verify database name in connection settings
3. Check if migrations ran successfully

## 📱 **Mobile Access:**

Adminer is responsive and works on mobile devices. Access the same URL from your phone/tablet when connected to the same network.

## 🔄 **Alternative Tools:**

If you prefer other database tools:

### **pgAdmin (More Features):**
```yaml
pgadmin:
  image: dpage/pgadmin4:latest
  environment:
    PGADMIN_DEFAULT_EMAIL: <EMAIL>
    PGADMIN_DEFAULT_PASSWORD: admin
  ports:
    - "5050:80"
```

### **PostgREST (API Interface):**
```yaml
postgrest:
  image: postgrest/postgrest
  environment:
    PGRST_DB_URI: *******************************************/auth_db
  ports:
    - "3000:3000"
```

Adminer provides the best balance of simplicity and functionality for database visualization and management!
