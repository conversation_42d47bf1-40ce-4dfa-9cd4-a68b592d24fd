# GORM AutoMigrate Implementation

## Overview

Replaced manual SQL migrations with GORM's AutoMigrate system for automatic database schema management based on Go struct models.

## ✅ **What Was Changed:**

### 1. **Replaced Migration System**
- **Before**: Manual SQL files in `migrations/` directories
- **After**: GORM AutoMigrate using Go struct models with GORM tags

### 2. **New Migration System (`coupon-shared-libs/database/migrations.go`)**
```go
type AutoMigrator struct {
    db     *DB
    logger *logging.Logger
}

// AutoMigrate runs GORM AutoMigrate on provided models
func (am *AutoMigrator) AutoMigrate(models ...interface{}) error

// AutoMigrateWithSeeds runs AutoMigrate and then executes seed functions
func (am *AutoMigrator) AutoMigrateWithSeeds(models []interface{}, seedFuncs ...func(*DB) error) error
```

### 3. **Auth Service Models**
- `UserCredential` - User authentication credentials
- `ServiceCredential` - Service-to-service authentication
- `RefreshToken` - JWT refresh tokens
- `Role` - User roles (ADMIN, USER)
- `UserRole` - Many-to-many user-role relationships

### 4. **User Service Models**
- `User` - User profile data with UserType (NEW, VIP)

### 5. **Seed System**
Created `coupon-auth-service/internal/seeds/auth_seeds.go`:
```go
func SeedDefaultRoles(db *database.DB) error {
    // Creates default ADMIN and USER roles
}
```

## 🔧 **Implementation Details:**

### **Auth Service Migration:**
```go
// Run GORM AutoMigrate
autoMigrator := database.NewAutoMigrator(db, logger)
models := []interface{}{
    &model.UserCredential{},
    &model.ServiceCredential{},
    &model.RefreshToken{},
    &model.Role{},
    &model.UserRole{},
}

if err := autoMigrator.AutoMigrateWithSeeds(models, seeds.SeedDefaultRoles); err != nil {
    logger.Fatalf("failed to run database migrations: %v", err)
}
```

### **User Service Migration:**
```go
// Run GORM AutoMigrate
autoMigrator := database.NewAutoMigrator(db, logger)
models := []interface{}{
    &model.User{},
}

if err := autoMigrator.AutoMigrate(models...); err != nil {
    logger.Fatalf("failed to run database migrations: %v", err)
}
```

## 📋 **Database Schema Generated:**

### **Auth Service Database:**
- `user_credentials` - Authentication data (no email field)
- `service_credentials` - Service authentication
- `refresh_tokens` - JWT refresh tokens
- `roles` - User roles with default ADMIN/USER
- `user_roles` - User-role assignments (many-to-many)

### **User Service Database:**
- `users` - User profiles with name, email, type (NEW/VIP)

## 🚀 **Benefits:**

1. **✅ Automatic Schema Management**: No manual SQL writing
2. **✅ Type Safety**: Schema defined in Go structs with compile-time checking
3. **✅ Version Control**: Models are versioned with code
4. **✅ Cross-Database Compatibility**: GORM handles different SQL dialects
5. **✅ Incremental Updates**: GORM only adds/modifies what's needed
6. **✅ Seed Data**: Automatic default role creation
7. **✅ No Migration Files**: Eliminates manual migration management

## 🔄 **Migration Process:**

1. **Service Startup**: AutoMigrator runs on each service start
2. **Schema Analysis**: GORM compares models with existing database schema
3. **Incremental Changes**: Only applies necessary changes (add columns, indexes, etc.)
4. **Seed Execution**: Runs seed functions after migration
5. **Logging**: Detailed logs of migration activities

## 📝 **Model Examples:**

### **Auth Service Model:**
```go
type UserCredential struct {
    ID           string    `gorm:"type:uuid;primary_key;"`
    UserID       string    `gorm:"type:uuid;not null;uniqueIndex"`
    PasswordHash string    `gorm:"not null"`
    CreatedAt    time.Time `gorm:"not null"`
    UpdatedAt    time.Time `gorm:"not null"`
}
```

### **User Service Model:**
```go
type User struct {
    ID        string    `gorm:"type:uuid;primary_key;"`
    Name      string    `gorm:"type:varchar(255);not null"`
    Email     string    `gorm:"type:varchar(255);not null;uniqueIndex"`
    Type      UserType  `gorm:"type:varchar(10);not null;default:'NEW'"`
    CreatedAt time.Time `gorm:"not null"`
    UpdatedAt time.Time `gorm:"not null"`
}
```

## 🧪 **Testing:**

```bash
# Rebuild and start services to test AutoMigrate
cd coupon-auth-service && docker-compose up --build -d
cd ../coupon-user-service && docker-compose up --build -d
cd ../coupon-api-gateway && docker-compose up --build -d
```

### **Expected Logs:**
```
INFO Starting GORM AutoMigrate for database schema
INFO Successfully migrated 5 models
INFO Running seed function 1
INFO Database migration and seeding completed successfully
```

### **Verify Schema:**

**Option 1: Database Studio (Recommended)**
- Auth Service Database: http://localhost:8080
- User Service Database: http://localhost:8090

**Option 2: Command Line**
```bash
# Check auth service tables
docker exec -it postgres-auth psql -U coupon -d auth_db -c "\dt"

# Check user service tables
docker exec -it postgres-user psql -U coupon -d user_db -c "\dt"
```

## 🔄 **Adding New Models:**

To add new models:

1. **Create Model**: Define struct with GORM tags
2. **Add to Migration**: Include in models slice
3. **Restart Service**: AutoMigrate will create new tables

Example:
```go
type NewModel struct {
    ID   string `gorm:"type:uuid;primary_key;"`
    Name string `gorm:"type:varchar(255);not null"`
}

// Add to models slice
models := []interface{}{
    &model.UserCredential{},
    &model.NewModel{}, // New model
}
```

## 🗑️ **Cleanup:**

- Removed `migrations/` directories from both services
- Updated Dockerfiles to not copy migration files
- Replaced manual SQL migration system entirely

The database schema is now fully managed through Go code with GORM AutoMigrate!
