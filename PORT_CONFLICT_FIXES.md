# Port Conflict Resolution and Infrastructure Optimization

## Summary of Changes

This document outlines the changes made to resolve port conflicts and optimize shared infrastructure across the coupon microservices.

## Issues Resolved

### 1. Port Conflicts
**Before**: All services used the same ports, causing conflicts when running simultaneously.
**After**: Each service now uses unique ports.

| Service | HTTP | gRPC | Metrics | PostgreSQL | Redis |
|---------|------|------|---------|------------|-------|
| Auth Service | 8081 | 50052 | 2113 | 5433 | 6380 |
| User Service | 8082 | 50053 | 2114 | 5434 | 6381 |
| API Gateway | 8083 | N/A | 2115 | N/A | N/A |
| **Shared Adminer** | **8080** | **N/A** | **N/A** | **All DBs** | **N/A** |

### 2. Infrastructure Optimization
**Before**: Each service had its own Jaeger, and some had duplicate Kafka/Zookeeper.
**After**: Shared infrastructure approach:

- **Shared <PERSON>aeger**: Single instance on ports 16686 (UI) and 6831 (UDP)
- **Shared Kafka**: Single instance on port 9092 (KRaft mode, no Zookeeper needed)
- **Separate Databases**: Each service maintains its own PostgreSQL and Redis

## Files Modified

### Docker Compose Files
- `coupon-auth-service/docker-compose.yml`: Added shared infrastructure, unique ports
- `coupon-user-service/docker-compose.yml`: Removed duplicate infrastructure, unique ports
- `coupon-api-gateway/docker-compose.yml`: Fixed container name, unique ports

### Configuration Files
- `coupon-auth-service/config/config.yaml`: Updated database/Redis hostnames
- `coupon-user-service/config/config.yaml`: Updated database/Redis/Kafka/Jaeger hostnames
- `coupon-api-gateway/config/config.yaml`: Updated service addresses and Jaeger hostname

### Documentation
- `README.md`: Updated with new port mappings and startup instructions
- `setup-network.sh`: New script to create shared Docker network

## Network Architecture

All services now use a shared Docker network `coupon-network` for inter-service communication:

```
coupon-network (Docker Network)
├── coupon-auth-service (container)
├── coupon-user-service (container)
├── coupon-api-gateway (container)
├── shared-jaeger (container)
├── shared-kafka (container)
├── postgres-auth (container)
├── postgres-user (container)
├── redis-auth (container)
├── redis-user (container)
└── shared-adminer (container) - Database Studio for All DBs
```

## Startup Sequence

**Critical**: Services must be started in this order:

1. **Auth Service** - Starts shared infrastructure (Jaeger, Kafka)
2. **User Service** - Connects to shared infrastructure
3. **API Gateway** - Connects to shared infrastructure

## Benefits

1. **No Port Conflicts**: All services can run simultaneously
2. **Resource Efficiency**: Single Jaeger and Kafka instances
3. **Simplified Monitoring**: All traces go to one Jaeger instance
4. **Proper Isolation**: Each service has its own database and Redis
5. **Modern Kafka**: Uses KRaft mode (no Zookeeper dependency)
6. **Scalable Architecture**: Easy to add new services to the shared network

## Testing the Setup

After making these changes, test by running:

```bash
# Setup network
./setup-network.sh

# Start services in order
cd coupon-auth-service && make compose-up
cd ../coupon-user-service && make compose-up  
cd ../coupon-api-gateway && make compose-up

# Verify all services are running
curl http://localhost:8081/health  # Auth Service
curl http://localhost:8082/health  # User Service
curl http://localhost:8083/health  # API Gateway
```

**Access Web Interfaces:**
- Jaeger UI: http://localhost:16686
- Shared Database Studio: http://localhost:8080 (connects to all databases)
