# Coupon Microservices

This repository contains multiple Go services that together form a coupon management platform. Each service can run independently using Docker Compose. This guide explains how to set up the entire system for local development.

## Repository Structure

- **coupon-api-gateway** – HTTP gateway that exposes public endpoints.
- **coupon-auth-service** – gRPC authentication service.
- **coupon-user-service** – Handles user management and publishes Kafka events.
- **coupon-proto** – Protobuf definitions and generated Go code.
- **coupon-shared-libs** – Common Go packages used by the services.

## Prerequisites

- [Go 1.24+](https://go.dev/)
- [Docker](https://www.docker.com/)
- [Docker Compose](https://docs.docker.com/compose/)
- `make` utility (optional, but used by the provided Makefiles)

## 1. Clone the Repository

```bash
git clone <repo-url>
cd coupon-microservices
```

## 2. Generate Protobuf Code

The API contracts are stored in `coupon-proto`. Before running the services you should generate the Go stubs:

```bash
cd coupon-proto
make install-deps    # installs buf and protoc plugins
make generate        # generates Go code under gen/
cd ..
```

## 3. Prepare Environment Files

Each service has an `.env.example` file. Copy it to `.env` and edit the values if needed:

```bash
cp coupon-auth-service/.env.example coupon-auth-service/.env
cp coupon-user-service/.env.example coupon-user-service/.env
cp coupon-api-gateway/.env.example coupon-api-gateway/.env
```

The default values expose the services on `localhost` with common ports.

## 4. Setup Shared Network

Before starting the services, create the shared Docker network:

```bash
./setup-network.sh
```

## 5. Start the Services

**Important**: Start the services in this specific order to ensure shared infrastructure (Jaeger, Kafka) is available:

```bash
# Terminal 1 – Auth Service (starts shared Jaeger, Kafka)
cd coupon-auth-service
make compose-up

# Terminal 2 – User Service
cd ../coupon-user-service
make compose-up

# Terminal 3 – API Gateway
cd ../coupon-api-gateway
make compose-up
```

The services now expose the following ports (no conflicts):

- **API Gateway** – HTTP `http://localhost:8083`, Metrics `localhost:2115`
- **Auth Service** – HTTP `localhost:8081`, gRPC `localhost:50052`, Metrics `localhost:2113`
- **User Service** – HTTP `localhost:8082`, gRPC `localhost:50053`, Metrics `localhost:2114`

### Database & Infrastructure Ports:
- **Auth Service PostgreSQL** – `localhost:5433`
- **User Service PostgreSQL** – `localhost:5434`
- **Auth Service Redis** – `localhost:6380`
- **User Service Redis** – `localhost:6381`
- **Shared Jaeger UI** – `http://localhost:16686`
- **Shared Kafka** – `localhost:9092` (KRaft mode, no Zookeeper needed)
- **Shared Database Studio** – `http://localhost:8080` 📊

To stop a service stack, run `make compose-down` inside that service directory.

## 6. Database Management

### **Shared Database Studio (Adminer)**
Access the web-based database management tool at: **http://localhost:8080**

**Database Connections:**
- **Auth Service Database**: Server: `postgres-auth`, User: `coupon`, Password: `coupon`, Database: `auth_db`
- **User Service Database**: Server: `postgres-user`, User: `coupon`, Password: `coupon`, Database: `user_db`

*Note: Use the same Adminer interface to connect to both databases by changing the server name in the login form.*

Features:
- 📊 Browse tables and data visually
- ✏️ Execute SQL queries with syntax highlighting
- 🔧 Edit records through web interface
- 📈 View database schema and relationships

See [DATABASE_STUDIO_SETUP.md](DATABASE_STUDIO_SETUP.md) for detailed usage instructions.

## 7. Additional Documentation

- Each service directory contains a dedicated `README.md` with more details about configuration and available Makefile targets.
- The [service_communication_matrix.md](service_communication_matrix.md) file documents how services communicate and which RPC methods are exposed.

## Troubleshooting

### Port Conflicts
All port conflicts have been resolved. Each service now uses unique ports:
- Services use different HTTP ports (8081, 8082, 8083)
- Services use different gRPC ports (50052, 50053)
- Each service has its own database and Redis instance with unique ports
- Shared infrastructure (Jaeger, Kafka, Zookeeper) is only started once by the auth service

### Network Issues
If you encounter network connectivity issues:
1. Ensure the shared network is created: `./setup-network.sh`
2. Start services in the correct order (auth service first)
3. Check that all containers are on the `coupon-network`

## License

This project is provided for educational purposes. See individual service directories for license information.