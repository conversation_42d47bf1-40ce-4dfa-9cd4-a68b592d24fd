# Service Registry Integration Example

This document provides a complete example of integrating a new service with the service registry system.

## Example: Product Service Integration

### 1. Configuration (`config/config.yaml`)

```yaml
service:
  name: "product-service"
  version: "1.0.0"
  environment: "development"
  port: 8080
  grpc_port: 50054
  client_id: "product-service"
  client_key: "${PRODUCT_SERVICE_API_KEY}"

database:
  host: "postgres-product"
  port: 5432
  user: "${POSTGRES_USER}"
  password: "${POSTGRES_PASSWORD}"
  name: "product_db"
  ssl_mode: "disable"

redis:
  host: "redis-product"
  port: 6379
  password: "${REDIS_PASSWORD}"
  db: 0

# Service Registry Configuration
service_registry:
  enabled: true
  database_host: "postgres-auth"  # Shared registry database
  database_port: 5432
  database_user: "auth_service"
  database_password: "123456789"
  database_name: "auth_db"
  heartbeat_interval: "30s"
  health_check_ttl: "90s"
  cleanup_interval: "60s"
  tags: ["product", "catalog", "inventory"]
  metadata:
    version: "1.0.0"
    team: "product"
    region: "us-east-1"

# Service Discovery Configuration
service_discovery:
  enabled: true
  refresh_interval: "30s"
  health_check_ttl: "90s"
  load_balancing: "round_robin"
  max_retries: 3
  retry_delay: "1s"
  cache_enabled: true
  cache_ttl: "60s"

downstream_services:
  auth_service_addr: "coupon-auth-service:50051"  # Fallback address
  user_service_addr: "coupon-user-service:50051"  # Fallback address

grpc:
  host: "0.0.0.0"
  port: 50054
  max_receive_size: 4194304
  max_send_size: 4194304
  connection_timeout: "5s"
  keepalive_time: "30s"
  keepalive_timeout: "5s"
  max_connection_idle: "90s"

jaeger:
  host: "shared-jaeger"
  port: 6831

logging:
  level: "debug"
  format: "json"

metrics:
  port: 2112
  path: "/metrics"
```

### 2. Main Service Implementation (`cmd/server/main.go`)

```go
package main

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"

	"github.com/labstack/echo/v4"
	productv1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/product/v1"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/auth"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/database"
	shared_grpc "gitlab.zalopay.vn/phunn4/coupon-shared-libs/grpc"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/health"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/metrics"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/redis"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/registry"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/tracing"
	"gitlab.zalopay.vn/phunn4/product-service/internal/handler/grpc"
	"gitlab.zalopay.vn/phunn4/product-service/internal/model"
	"gitlab.zalopay.vn/phunn4/product-service/internal/repository"
	"gitlab.zalopay.vn/phunn4/product-service/internal/service"
)

func main() {
	cfg, err := config.Load()
	if err != nil {
		panic(fmt.Sprintf("failed to load config: %v", err))
	}

	logger := logging.New(cfg.Logging.Level, cfg.Logging.Format)
	logger.Infof("Starting service: %s v%s", cfg.Service.Name, cfg.Service.Version)

	tracer, err := tracing.New(cfg.Service.Name, cfg.Jaeger.Host, cfg.Jaeger.Port)
	if err != nil {
		logger.Fatalf("failed to initialize tracer: %v", err)
	}

	appMetrics := metrics.New(cfg.Service.Name)
	jwtManager := auth.NewJWTManager(&cfg.Auth)

	// Database setup
	db, err := database.NewPostgresDB(&cfg.Database, logger, appMetrics)
	if err != nil {
		logger.Fatalf("failed to connect to database: %v", err)
	}

	// Auto-migrate models
	autoMigrator := database.NewAutoMigrator(db, logger)
	models := []interface{}{
		&model.Product{},
		&model.Category{},
	}

	if err := autoMigrator.AutoMigrate(models...); err != nil {
		logger.Fatalf("failed to run database migrations: %v", err)
	}

	redisClient := redis.NewClient(&cfg.Redis, logger, appMetrics)

	// Business logic setup
	repo := repository.NewProductRepository(db, redisClient, logger)
	svc := service.NewProductService(repo, logger)

	// Health checker setup
	healthChecker := health.NewHealthChecker()
	healthChecker.AddCheck("database", db.Health)
	healthChecker.AddCheck("redis", redisClient.Health)

	// Initialize service registry manager
	registryManager, err := registry.NewManager(cfg, logger)
	if err != nil {
		logger.Fatalf("Failed to create registry manager: %v", err)
	}

	// Add service endpoints to registry
	if registryManager.IsRegistryEnabled() {
		registryManager.AddServiceEndpoint("GetProduct", "/product.v1.ProductService/GetProduct", "POST", "GRPC", false, "Get product by ID")
		registryManager.AddServiceEndpoint("ListProducts", "/product.v1.ProductService/ListProducts", "POST", "GRPC", false, "List products with pagination")
		registryManager.AddServiceEndpoint("CreateProduct", "/product.v1.ProductService/CreateProduct", "POST", "GRPC", false, "Create new product")
		registryManager.AddServiceEndpoint("UpdateProduct", "/product.v1.ProductService/UpdateProduct", "POST", "GRPC", false, "Update existing product")
		registryManager.AddServiceEndpoint("DeleteProduct", "/product.v1.ProductService/DeleteProduct", "POST", "GRPC", false, "Delete product")
		registryManager.AddServiceEndpoint("GetCategory", "/product.v1.ProductService/GetCategory", "POST", "GRPC", false, "Get category by ID")
		registryManager.AddServiceEndpoint("ListCategories", "/product.v1.ProductService/ListCategories", "POST", "GRPC", false, "List all categories")
		registryManager.AddServiceEndpoint("HealthCheck", "/product.v1.ProductService/HealthCheck", "POST", "GRPC", true, "Service health check")
		
		// Add health checks to registry
		registryManager.AddHealthCheck("database", registry.CreateDatabaseHealthCheck(db.Health))
		registryManager.AddHealthCheck("redis", registry.CreateRedisHealthCheck(redisClient.Health))
		
		// Add custom health check
		registryManager.AddHealthCheck("business_logic", registry.CreateCustomHealthCheck("business", func(ctx context.Context) error {
			// Check if we can perform basic business operations
			_, err := repo.GetProductCount(ctx)
			return err
		}))
	}

	ctx, stop := signal.NotifyContext(context.Background(), os.Interrupt, syscall.SIGTERM)
	defer stop()

	// Start service registry
	if err := registryManager.Start(ctx); err != nil {
		logger.Fatalf("Failed to start registry manager: %v", err)
	}

	var wg sync.WaitGroup
	wg.Add(2)

	go func() {
		defer wg.Done()
		startGRPCServer(ctx, cfg, logger, appMetrics, svc, jwtManager.AuthFunc)
	}()
	go func() {
		defer wg.Done()
		startHTTPServer(ctx, cfg, logger, healthChecker, appMetrics)
	}()

	wg.Wait()
	
	// Stop service registry
	if err := registryManager.Stop(ctx); err != nil {
		logger.Errorf("Failed to stop registry manager: %v", err)
	}
	
	tracer.Close()
	db.Close()
	redisClient.Close()
	logger.Info("Shutdown complete")
}

func startGRPCServer(ctx context.Context, cfg *config.Config, logger *logging.Logger, metrics *metrics.Metrics, svc service.ProductService, authFunc func(context.Context) (context.Context, error)) {
	grpcServerWrapper := shared_grpc.NewServer(&cfg.GRPC, logger, metrics, authFunc)
	grpcHandler := grpc.NewProductServer(svc)
	productv1.RegisterProductServiceServer(grpcServerWrapper.Server, grpcHandler)

	go func() {
		if err := grpcServerWrapper.Start(); err != nil {
			logger.Errorf("gRPC server failed: %v", err)
		}
	}()

	<-ctx.Done()
	grpcServerWrapper.Stop()
}

func startHTTPServer(ctx context.Context, cfg *config.Config, logger *logging.Logger, healthChecker *health.HealthChecker, metrics *metrics.Metrics) {
	e := echo.New()
	e.GET("/health", healthChecker.HTTPHandler())
	e.GET(cfg.Metrics.Path, echo.WrapHandler(metrics.Handler()))

	addr := fmt.Sprintf(":%d", cfg.Service.Port)
	server := &http.Server{Addr: addr, Handler: e}

	logger.Infof("Starting operational HTTP server on %s", addr)

	go func() {
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Fatalf("HTTP server failed: %v", err)
		}
	}()

	<-ctx.Done()

	shutdownCtx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err := server.Shutdown(shutdownCtx); err != nil {
		logger.Fatalf("HTTP server shutdown failed: %v", err)
	}
}
```

### 3. Client Usage Example

```go
package main

import (
	"context"
	"log"

	productv1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/product/v1"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/metrics"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/registry"
)

func main() {
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	logger := logging.New("info", "json")
	appMetrics := metrics.New("client-example")

	// Initialize registry manager for discovery only
	registryManager, err := registry.NewManager(cfg, logger)
	if err != nil {
		log.Fatalf("Failed to create registry manager: %v", err)
	}

	ctx := context.Background()

	// Method 1: Direct service discovery
	instances, err := registryManager.DiscoverService(ctx, "product-service")
	if err != nil {
		log.Fatalf("Failed to discover product service: %v", err)
	}

	log.Printf("Found %d product service instances:", len(instances))
	for _, instance := range instances {
		log.Printf("  - %s (Status: %s, Health: %s)", 
			instance.GetGRPCAddress(), instance.Status, instance.HealthStatus)
	}

	// Method 2: Get service address with load balancing
	address, err := registryManager.GetServiceAddress(ctx, "product-service")
	if err != nil {
		log.Fatalf("Failed to get product service address: %v", err)
	}

	log.Printf("Selected product service address: %s", address)

	// Method 3: Use registry-aware client (recommended)
	serviceClient, err := registry.NewServiceClient(
		"product-service",
		registryManager,
		&cfg.GRPC,
		logger,
		appMetrics,
		cfg.Service.ClientID,
		cfg.Service.ClientKey,
	)
	if err != nil {
		log.Fatalf("Failed to create service client: %v", err)
	}
	defer serviceClient.Close()

	// Get connection and create gRPC client
	conn, err := serviceClient.GetConnection(ctx)
	if err != nil {
		log.Fatalf("Failed to get connection: %v", err)
	}

	productClient := productv1.NewProductServiceClient(conn)

	// Make gRPC call
	response, err := productClient.ListProducts(ctx, &productv1.ListProductsRequest{
		PageSize: 10,
		PageToken: "",
	})
	if err != nil {
		log.Fatalf("Failed to list products: %v", err)
	}

	log.Printf("Retrieved %d products", len(response.Products))
}
```

### 4. Docker Compose Integration

```yaml
services:
  postgres-product:
    image: postgres:16-alpine
    container_name: postgres-product
    environment:
      POSTGRES_USER: ${POSTGRES_USER:-coupon}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-coupon}
      POSTGRES_DB: ${POSTGRES_DB:-product_db}
    ports:
      - "5435:5432"
    networks:
      - coupon-network

  redis-product:
    image: redis:7-alpine
    container_name: redis-product
    command: ["redis-server", "--requirepass", "${REDIS_PASSWORD:-}"]
    ports:
      - "6382:6379"
    networks:
      - coupon-network

  product-service:
    build:
      context: .
      args:
        GITLAB_USER: ${GITLAB_USER}
        GITLAB_TOKEN: ${GITLAB_TOKEN}
    container_name: product-service
    image: registry-gitlab.zalopay.vn/phunn4/coupon-product-service
    depends_on:
      postgres-product:
        condition: service_healthy
      redis-product:
        condition: service_started
    env_file:
      - .env
    ports:
      - "8084:8080"  # HTTP port
      - "50054:50054" # gRPC port
      - "2115:2112"   # Metrics port
    restart: unless-stopped
    networks:
      - coupon-network

networks:
  coupon-network:
    name: coupon-network
    driver: bridge
    external: true
```

### 5. Testing the Integration

```bash
# 1. Check service registration
curl http://localhost:8084/health

# 2. Query registry database
docker exec -it postgres-auth psql -U auth_service -d auth_db -c "
SELECT service_name, host, grpc_port, status, health_status, last_heartbeat 
FROM service_instances 
WHERE service_name = 'product-service' AND deleted_at IS NULL;
"

# 3. Test service discovery from another service
docker exec -it auth-service curl http://localhost:8080/health

# 4. Check service endpoints
docker exec -it postgres-auth psql -U auth_service -d auth_db -c "
SELECT si.service_name, se.name, se.path, se.is_public 
FROM service_instances si 
JOIN service_endpoints se ON si.id = se.service_id 
WHERE si.service_name = 'product-service' AND si.deleted_at IS NULL;
"
```

This example demonstrates a complete integration of a new service with the service registry system, including configuration, registration, health checking, and client usage patterns.
