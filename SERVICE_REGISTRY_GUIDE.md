# Service Registry Implementation Guide

## Overview

This document describes the service registration and discovery mechanism implemented for the coupon microservice architecture. The solution provides a database-based service registry with client-side discovery, designed to be simple, maintainable, and compatible with the existing gRPC-based communication patterns.

## Architecture

### Core Components

1. **Service Registry Database**: PostgreSQL-based storage for service instances, endpoints, and health status
2. **Service Registrar**: Handles service self-registration and heartbeat management
3. **Health Reporter**: Monitors service health and updates registry status
4. **Discovery Client**: Provides service discovery with caching and load balancing
5. **Registry Manager**: Orchestrates all registry components

### Design Principles

- **Simplicity**: Minimal complexity with straightforward interfaces
- **Database-based**: Leverages existing PostgreSQL infrastructure
- **Client-side Discovery**: Services discover and connect to each other directly
- **Health-aware**: Automatic deregistration of unhealthy services
- **gRPC Integration**: Seamless integration with existing gRPC patterns

## Configuration

### Service Registry Configuration

Add to your service's `config.yaml`:

```yaml
service_registry:
  enabled: true
  database_host: "postgres-auth"  # Shared registry database
  database_port: 5432
  database_user: "auth_service"
  database_password: "123456789"
  database_name: "auth_db"
  heartbeat_interval: "30s"       # How often to send heartbeats
  health_check_ttl: "90s"         # When to consider service expired
  cleanup_interval: "60s"         # How often to cleanup expired services
  tags: ["auth", "authentication"] # Service tags for categorization
  metadata:
    version: "1.0.0"
    team: "platform"

service_discovery:
  enabled: true
  refresh_interval: "30s"         # How often to refresh service cache
  health_check_ttl: "90s"         # Consider services expired after this
  load_balancing: "round_robin"   # Load balancing strategy
  max_retries: 3                  # Max retry attempts
  retry_delay: "1s"               # Delay between retries
  cache_enabled: true             # Enable service caching
  cache_ttl: "60s"                # Cache expiration time
```

### Database Schema

The registry uses three main tables:

1. **service_instances**: Core service registration data
2. **service_endpoints**: Available endpoints per service
3. **service_health**: Health check history

## Integration Guide

### 1. Service Registration

Add to your service's `main.go`:

```go
import (
    "gitlab.zalopay.vn/phunn4/coupon-shared-libs/registry"
)

func main() {
    // ... existing setup ...
    
    // Initialize service registry manager
    registryManager, err := registry.NewManager(cfg, logger)
    if err != nil {
        logger.Fatalf("Failed to create registry manager: %v", err)
    }

    // Add service endpoints
    if registryManager.IsRegistryEnabled() {
        registryManager.AddServiceEndpoint("Login", "/auth.v1.AuthService/Login", "POST", "GRPC", true, "User authentication")
        registryManager.AddServiceEndpoint("ValidateToken", "/auth.v1.AuthService/ValidateUserToken", "POST", "GRPC", false, "Validate JWT token")
        
        // Add health checks
        registryManager.AddHealthCheck("database", registry.CreateDatabaseHealthCheck(db.Health))
        registryManager.AddHealthCheck("redis", registry.CreateRedisHealthCheck(redisClient.Health))
    }

    // Start registry
    if err := registryManager.Start(ctx); err != nil {
        logger.Fatalf("Failed to start registry manager: %v", err)
    }
    
    // ... start your services ...
    
    // Stop registry on shutdown
    if err := registryManager.Stop(ctx); err != nil {
        logger.Errorf("Failed to stop registry manager: %v", err)
    }
}
```

### 2. Service Discovery

For services that need to discover other services:

```go
// Discover all healthy instances of a service
instances, err := registryManager.DiscoverService(ctx, "auth-service")
if err != nil {
    return err
}

// Get a single service address (with load balancing)
address, err := registryManager.GetServiceAddress(ctx, "user-service")
if err != nil {
    return err
}

// Use the address for gRPC connection
conn, err := grpc.Dial(address, grpc.WithInsecure())
```

### 3. Enhanced gRPC Client (Optional)

Use the registry-aware client for automatic service discovery:

```go
// Create a service client with discovery
serviceClient, err := registry.NewServiceClient(
    "auth-service",
    registryManager,
    &cfg.GRPC,
    logger,
    metrics,
    cfg.Service.ClientID,
    cfg.Service.ClientKey,
)

// Use like a regular gRPC connection
conn, err := serviceClient.GetConnection(ctx)
```

## Health Checking

### Built-in Health Checks

The system provides built-in health check functions:

```go
// Database health check
registryManager.AddHealthCheck("database", registry.CreateDatabaseHealthCheck(db.Health))

// Redis health check
registryManager.AddHealthCheck("redis", registry.CreateRedisHealthCheck(redisClient.Health))

// Custom health check
registryManager.AddHealthCheck("custom", registry.CreateCustomHealthCheck("api", func(ctx context.Context) error {
    // Your custom health check logic
    return nil
}))
```

### Health Check Lifecycle

1. **Registration**: Service registers with `STARTING` status
2. **Health Reporting**: Periodic health checks update status to `HEALTHY` or `UNHEALTHY`
3. **Heartbeat**: Regular heartbeats maintain registration
4. **Cleanup**: Expired services are automatically deregistered

## Service Lifecycle

### Registration Process

1. Service starts and creates registry manager
2. Manager connects to registry database
3. Service instance is registered with `STARTING` status
4. Endpoints are registered
5. Health reporter starts periodic checks
6. Service status updates to `HEALTHY` when all checks pass

### Discovery Process

1. Client requests service instances
2. Registry returns healthy instances from database
3. Client caches results (if enabled)
4. Load balancing selects instance
5. Connection is established

### Deregistration Process

1. Service receives shutdown signal
2. Registry manager stops health reporter
3. Service is marked as `DEREGISTERED`
4. Cleanup process removes expired entries

## Monitoring and Observability

### Metrics

The registry automatically records metrics for:
- Service registration/deregistration events
- Health check results and duration
- Discovery requests and cache hits
- gRPC client connection attempts

### Logging

Structured logging provides visibility into:
- Service lifecycle events
- Health check results
- Discovery operations
- Connection management

### Health Endpoints

Each service exposes health information via:
- HTTP endpoint: `GET /health`
- gRPC health check protocol (optional)

## Best Practices

### Service Configuration

1. **Use shared registry database**: All services should connect to the same registry database
2. **Configure appropriate TTLs**: Balance between responsiveness and database load
3. **Enable caching**: Reduces database queries for discovery
4. **Set meaningful tags**: Help with service categorization and filtering

### Health Checks

1. **Keep checks lightweight**: Health checks run frequently
2. **Check critical dependencies**: Database, Redis, external APIs
3. **Use appropriate timeouts**: Prevent hanging health checks
4. **Implement graceful degradation**: Service can be partially healthy

### Error Handling

1. **Graceful fallbacks**: Fall back to configured addresses if discovery fails
2. **Retry logic**: Implement retries for transient failures
3. **Circuit breakers**: Prevent cascading failures
4. **Monitoring**: Alert on discovery failures

## Troubleshooting

### Common Issues

1. **Service not discovered**:
   - Check if service is registered: Query `service_instances` table
   - Verify health status: Check `service_health` table
   - Confirm TTL settings: Ensure heartbeat interval < health check TTL

2. **Connection failures**:
   - Verify network connectivity between services
   - Check gRPC port configuration
   - Validate authentication credentials

3. **Performance issues**:
   - Enable caching to reduce database queries
   - Adjust refresh intervals
   - Monitor database connection pool

### Database Queries

Useful queries for debugging:

```sql
-- List all registered services
SELECT * FROM service_instances WHERE deleted_at IS NULL;

-- Check service health
SELECT si.service_name, si.status, si.health_status, si.last_heartbeat
FROM service_instances si
WHERE si.deleted_at IS NULL;

-- View service endpoints
SELECT si.service_name, se.name, se.path, se.protocol
FROM service_instances si
JOIN service_endpoints se ON si.id = se.service_id
WHERE si.deleted_at IS NULL;
```

## Migration Guide

### From Static Configuration

1. **Phase 1**: Deploy registry-enabled services with discovery disabled
2. **Phase 2**: Enable discovery while keeping static fallbacks
3. **Phase 3**: Remove static configuration once discovery is stable

### Rollback Plan

Services can fall back to static configuration by:
1. Disabling service discovery in configuration
2. Using hardcoded service addresses
3. Maintaining existing client connection patterns

## Future Enhancements

Potential improvements for future versions:

1. **Advanced Load Balancing**: Weighted round-robin, least connections
2. **Service Mesh Integration**: Istio/Linkerd compatibility
3. **Multi-Region Support**: Cross-region service discovery
4. **Admin Interface**: Web UI for service management
5. **Metrics Dashboard**: Grafana dashboards for monitoring
6. **Automated Testing**: Integration tests for discovery scenarios

## Conclusion

This service registry implementation provides a solid foundation for service discovery in the coupon microservice architecture. It balances simplicity with functionality, ensuring maintainability while providing the core features needed for dynamic service communication.

The database-based approach leverages existing infrastructure and provides strong consistency, while the client-side discovery pattern ensures good performance and resilience. The health checking system ensures that only healthy services participate in request routing, improving overall system reliability.
