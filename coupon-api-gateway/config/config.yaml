service:
  name: "api-gateway"
  version: "1.0.0"
  environment: "development"
  port: 8080
  client_id: "api-gateway-client"
  client_key: "${API_GATEWAY_KEY}"

downstream_services:
  auth_service_addr: "coupon-auth-service:50051"
  user_service_addr: "coupon-user-service:50051"
  order_service_addr: "order-service:50051"
  coupon_service_addr: "coupon-service:50051"
  product_service_addr: "product-service:50051"

auth:
  jwt_secret: "${JWT_SECRET_KEY}"
  jwt_expiration: "1h"

jaeger:
  host: "shared-jaeger"
  port: 6831

logging:
  level: "debug"
  format: "json"

metrics:
  port: 2112
  path: "/metrics"
