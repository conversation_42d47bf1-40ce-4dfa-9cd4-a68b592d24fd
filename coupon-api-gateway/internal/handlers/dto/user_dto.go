package dto

import (
	"time"

	proto_user_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/user/v1"
)

type UserResponse struct {
	ID        string    `json:"id"`
	Email     string    `json:"email"`
	Name      string    `json:"name"`
	Roles     []string  `json:"roles"`
	Type      string    `json:"type"`
	CreatedAt time.Time `json:"created_at"`
}

func ToUserResponse(user *proto_user_v1.User) *UserResponse {
	if user == nil {
		return nil
	}
	return &UserResponse{
		ID:        user.Id,
		Email:     user.Email,
		Name:      user.Name,
		Roles:     user.Roles,
		Type:      convertUserTypeToString(user.Type),
		CreatedAt: user.CreatedAt.AsTime(),
	}
}

// convertUserTypeToString converts proto UserType to string
func convertUserTypeToString(userType proto_user_v1.UserType) string {
	switch userType {
	case proto_user_v1.UserType_USER_TYPE_NEW:
		return "NEW"
	case proto_user_v1.UserType_USER_TYPE_VIP:
		return "VIP"
	default:
		return "NEW" // Default to NEW for unspecified
	}
}
