package middleware

import (
	"net/http"
	"strings"

	"github.com/labstack/echo/v4"
	"gitlab.zalopay.vn/phunn4/coupon-api-gateway/internal/clients"
	"gitlab.zalopay.vn/phunn4/coupon-api-gateway/internal/utils"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/auth"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
)

// CookieAuthConfig holds configuration for cookie-based authentication
type CookieAuthConfig struct {
	AuthClient    *clients.AuthClient
	JWTManager    *auth.JWTManager
	CookieConfig  *utils.CookieConfig
	Logger        *logging.Logger
	SkipPaths     []string // Paths that don't require authentication
}

// CookieAuth creates a middleware for cookie-based authentication
func CookieAuth(config *CookieAuthConfig) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			// Skip authentication for certain paths
			if shouldSkipAuth(c.Request().URL.Path, config.SkipPaths) {
				return next(c)
			}

			// Try to get access token from cookie
			accessToken, err := utils.GetAccessTokenFromCookie(c)
			if err != nil {
				// No access token cookie, try to refresh
				return handleMissingAccessToken(c, config, next)
			}

			// Validate the access token
			claims, err := config.JWTManager.ValidateToken(accessToken)
			if err != nil {
				// Access token is invalid, try to refresh
				config.Logger.WithContext(c.Request().Context()).
					Debugf("Access token validation failed: %v", err)
				return handleInvalidAccessToken(c, config, next)
			}

			// Token is valid, set user context
			c.Set("userID", claims.UserID)
			c.Set("userEmail", claims.Email)
			c.Set("userRoles", claims.Roles)

			return next(c)
		}
	}
}

// handleMissingAccessToken handles the case when no access token is found
func handleMissingAccessToken(c echo.Context, config *CookieAuthConfig, next echo.HandlerFunc) error {
	// Try to get refresh token
	refreshToken, err := utils.GetRefreshTokenFromCookie(c)
	if err != nil {
		// No refresh token either, require login
		return echo.NewHTTPError(http.StatusUnauthorized, "Authentication required")
	}

	// Try to refresh the access token
	return attemptTokenRefresh(c, config, refreshToken, next)
}

// handleInvalidAccessToken handles the case when access token is invalid
func handleInvalidAccessToken(c echo.Context, config *CookieAuthConfig, next echo.HandlerFunc) error {
	// Try to get refresh token
	refreshToken, err := utils.GetRefreshTokenFromCookie(c)
	if err != nil {
		// No refresh token, clear cookies and require login
		utils.ClearAuthCookies(c, config.CookieConfig)
		return echo.NewHTTPError(http.StatusUnauthorized, "Authentication required")
	}

	// Try to refresh the access token
	return attemptTokenRefresh(c, config, refreshToken, next)
}

// attemptTokenRefresh tries to refresh the access token using the refresh token
func attemptTokenRefresh(c echo.Context, config *CookieAuthConfig, refreshToken string, next echo.HandlerFunc) error {
	ctx := c.Request().Context()
	
	// Validate refresh token first
	refreshClaims, err := config.JWTManager.ValidateToken(refreshToken)
	if err != nil {
		// Refresh token is also invalid, clear cookies and require login
		utils.ClearAuthCookies(c, config.CookieConfig)
		config.Logger.WithContext(ctx).
			Debugf("Refresh token validation failed: %v", err)
		return echo.NewHTTPError(http.StatusUnauthorized, "Authentication required")
	}

	// TODO: Call auth service to refresh tokens
	// For now, we'll validate the refresh token and continue
	// In a full implementation, you would call the auth service to get new tokens
	
	// Set user context from refresh token claims
	c.Set("userID", refreshClaims.UserID)
	c.Set("userEmail", refreshClaims.Email)
	c.Set("userRoles", refreshClaims.Roles)

	config.Logger.WithContext(ctx).
		Debugf("Using refresh token for user: %s", refreshClaims.UserID)

	return next(c)
}

// shouldSkipAuth checks if authentication should be skipped for the given path
func shouldSkipAuth(path string, skipPaths []string) bool {
	for _, skipPath := range skipPaths {
		if strings.HasPrefix(path, skipPath) {
			return true
		}
	}
	return false
}

// RequireAuth is a stricter middleware that always requires authentication
func RequireAuth(config *CookieAuthConfig) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			// Try to get access token from cookie
			accessToken, err := utils.GetAccessTokenFromCookie(c)
			if err != nil {
				return echo.NewHTTPError(http.StatusUnauthorized, "Access token required")
			}

			// Validate the access token
			claims, err := config.JWTManager.ValidateToken(accessToken)
			if err != nil {
				utils.ClearAuthCookies(c, config.CookieConfig)
				return echo.NewHTTPError(http.StatusUnauthorized, "Invalid access token")
			}

			// Token is valid, set user context
			c.Set("userID", claims.UserID)
			c.Set("userEmail", claims.Email)
			c.Set("userRoles", claims.Roles)

			return next(c)
		}
	}
}

// RequireRole creates a middleware that requires specific roles
func RequireRole(roles ...string) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			userRoles, ok := c.Get("userRoles").([]string)
			if !ok {
				return echo.NewHTTPError(http.StatusForbidden, "User roles not found")
			}

			// Check if user has any of the required roles
			for _, requiredRole := range roles {
				for _, userRole := range userRoles {
					if userRole == requiredRole {
						return next(c)
					}
				}
			}

			return echo.NewHTTPError(http.StatusForbidden, "Insufficient permissions")
		}
	}
}
