package utils

import (
	"net/http"
	"time"

	"github.com/labstack/echo/v4"
)

const (
	// Cookie names
	AccessTokenCookie  = "access_token"
	RefreshTokenCookie = "refresh_token"
	
	// Cookie settings
	AccessTokenExpiry  = 24 * time.Hour    // 24 hours
	RefreshTokenExpiry = 7 * 24 * time.Hour // 7 days
)

// CookieConfig holds cookie configuration
type CookieConfig struct {
	Domain   string
	Secure   bool
	HTTPOnly bool
	SameSite http.SameSite
}

// NewCookieConfig creates a new cookie configuration
func NewCookieConfig(domain string, secure bool) *CookieConfig {
	return &CookieConfig{
		Domain:   domain,
		Secure:   secure,
		HTTPOnly: true,
		SameSite: http.SameSiteStrictMode,
	}
}

// SetAuthCookies sets both access and refresh token cookies
func SetAuthCookies(c echo.Context, accessToken, refreshToken string, config *CookieConfig) {
	SetAccessTokenCookie(c, accessToken, config)
	SetRefreshTokenCookie(c, refreshToken, config)
}

// SetAccessTokenCookie sets the access token cookie
func SetAccessTokenCookie(c echo.Context, token string, config *CookieConfig) {
	cookie := &http.Cookie{
		Name:     AccessTokenCookie,
		Value:    token,
		Path:     "/",
		Domain:   config.Domain,
		Expires:  time.Now().Add(AccessTokenExpiry),
		MaxAge:   int(AccessTokenExpiry.Seconds()),
		Secure:   config.Secure,
		HttpOnly: config.HTTPOnly,
		SameSite: config.SameSite,
	}
	c.SetCookie(cookie)
}

// SetRefreshTokenCookie sets the refresh token cookie
func SetRefreshTokenCookie(c echo.Context, token string, config *CookieConfig) {
	cookie := &http.Cookie{
		Name:     RefreshTokenCookie,
		Value:    token,
		Path:     "/",
		Domain:   config.Domain,
		Expires:  time.Now().Add(RefreshTokenExpiry),
		MaxAge:   int(RefreshTokenExpiry.Seconds()),
		Secure:   config.Secure,
		HttpOnly: config.HTTPOnly,
		SameSite: config.SameSite,
	}
	c.SetCookie(cookie)
}

// GetAccessTokenFromCookie retrieves the access token from cookies
func GetAccessTokenFromCookie(c echo.Context) (string, error) {
	cookie, err := c.Cookie(AccessTokenCookie)
	if err != nil {
		return "", err
	}
	return cookie.Value, nil
}

// GetRefreshTokenFromCookie retrieves the refresh token from cookies
func GetRefreshTokenFromCookie(c echo.Context) (string, error) {
	cookie, err := c.Cookie(RefreshTokenCookie)
	if err != nil {
		return "", err
	}
	return cookie.Value, nil
}

// ClearAuthCookies clears both access and refresh token cookies
func ClearAuthCookies(c echo.Context, config *CookieConfig) {
	ClearAccessTokenCookie(c, config)
	ClearRefreshTokenCookie(c, config)
}

// ClearAccessTokenCookie clears the access token cookie
func ClearAccessTokenCookie(c echo.Context, config *CookieConfig) {
	cookie := &http.Cookie{
		Name:     AccessTokenCookie,
		Value:    "",
		Path:     "/",
		Domain:   config.Domain,
		Expires:  time.Unix(0, 0),
		MaxAge:   -1,
		Secure:   config.Secure,
		HttpOnly: config.HTTPOnly,
		SameSite: config.SameSite,
	}
	c.SetCookie(cookie)
}

// ClearRefreshTokenCookie clears the refresh token cookie
func ClearRefreshTokenCookie(c echo.Context, config *CookieConfig) {
	cookie := &http.Cookie{
		Name:     RefreshTokenCookie,
		Value:    "",
		Path:     "/",
		Domain:   config.Domain,
		Expires:  time.Unix(0, 0),
		MaxAge:   -1,
		Secure:   config.Secure,
		HttpOnly: config.HTTPOnly,
		SameSite: config.SameSite,
	}
	c.SetCookie(cookie)
}
