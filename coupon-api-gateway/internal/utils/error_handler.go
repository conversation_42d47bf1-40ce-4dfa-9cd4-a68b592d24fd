package utils

import (
	"net/http"

	"github.com/labstack/echo/v4"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

type ErrorResponse struct {
	Message string `json:"message"`
	Code    string `json:"code,omitempty"`
}

func NewErrorResponse(message string) ErrorResponse {
	return ErrorResponse{Message: message}
}

func HandleGRPCError(c echo.Context, err error, logger *logging.Logger) error {
	log := logger.WithContext(c.Request().Context())
	st, ok := status.FromError(err)
	if !ok {
		log.Errorf("Non-gRPC error in gRPC client call: %v", err)
		return c.JSON(http.StatusInternalServerError, NewErrorResponse("internal server error"))
	}

	var httpStatus int
	switch st.Code() {
	case codes.InvalidArgument:
		httpStatus = http.StatusBadRequest
	case codes.NotFound:
		httpStatus = http.StatusNotFound
	case codes.AlreadyExists:
		httpStatus = http.StatusConflict
	case codes.PermissionDenied:
		httpStatus = http.StatusForbidden
	case codes.Unauthenticated:
		httpStatus = http.StatusUnauthorized
	case codes.Unavailable:
		httpStatus = http.StatusServiceUnavailable
	default:
		httpStatus = http.StatusInternalServerError
	}

	log.Warnf("gRPC error handled: code=%s, message=%s", st.Code(), st.Message())
	return c.JSON(httpStatus, NewErrorResponse(st.Message()))
}

// SuccessResponse represents a successful API response
type SuccessResponse struct {
	Message string `json:"message"`
}

// NewSuccessResponse creates a new success response
func NewSuccessResponse(message string) *SuccessResponse {
	return &SuccessResponse{Message: message}
}
