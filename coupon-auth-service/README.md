# Coupon Auth Service

This service provides authentication and authorization functionality for other coupon microservices. It exposes a gRPC API and a small HTTP server for health checks and metrics.

## Requirements

- Go 1.24+
- Docker (for containerised development)

## Configuration

Configuration is loaded from `config/config.yaml` and environment variables. A sample `.env.example` file is provided. Copy it to `.env` and adjust the values if you plan to run locally or with Docker Compose.

## Makefile Targets

- `make build` – build the Go binary
- `make run` – run the service directly
- `make test` – run Go tests
- `make docker-build` – build the Docker image
- `make compose-up` – start the service and dependencies using Docker Compose
- `make compose-down` – stop the Docker Compose services

## Running with Docker Compose

1. Copy `.env.example` to `.env` and fill in the required values.
2. Run `make compose-up` to build and start the service along with Postgres, Redis and Jaeger.
3. The service will be available on `localhost:8080` for HTTP, `localhost:50051` for gRPC and `localhost:2112` for metrics.

Use `make compose-down` to stop the containers.

## Testing

Run `make test` to execute unit tests. Note that downloading dependencies may require internet access.
