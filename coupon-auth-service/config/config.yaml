service:
  name: "auth-service"
  version: "1.0.0"
  environment: "development"
  port: 8080
  grpc_port: 50051
  client_id: "auth-service"
  client_key: "x1q2cws34dev5yuimo"

database:
  host: "postgres-auth"
  port: 5432
  user: "auth_service"
  password: "123456789"
  name: "auth_db"
  ssl_mode: "disable"
  max_open_conns: 10
  max_idle_conns: 5
  max_lifetime: "1h"

redis:
  host: "redis-auth"
  port: 6379
  password: "123456789"
  db: 0
  pool_size: 10
  dial_timeout: "5s"
  read_timeout: "3s"
  write_timeout: "3s"

auth:
  jwt_secret: "ca4t3s5d6f7g8hj"
  jwt_expiration: "24h"

downstream_services:
  user_service_addr: "coupon-user-service:50051"

jaeger:
  host: "shared-jaeger"
  port: 6831

logging:
  level: "debug"
  format: "json"

metrics:
  port: 2112
  path: "/metrics"

service_registry:
  enabled: true
  database_host: "postgres-auth"
  database_port: 5432
  database_user: "auth_service"
  database_password: "123456789"
  database_name: "auth_db"

service_discovery:
  enabled: true

grpc:
  host: "0.0.0.0"
  port: 50051
  max_receive_size: 4194304
  max_send_size: 4194304
  connection_timeout: "5s"
  keepalive_time: "30s"
  keepalive_timeout: "5s"
  max_connection_idle: "90s"
