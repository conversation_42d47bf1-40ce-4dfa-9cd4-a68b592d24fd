package clients

import (
	"context"
	"fmt"
	"time"

	proto_user_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/user/v1"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	shared_grpc "gitlab.zalopay.vn/phunn4/coupon-shared-libs/grpc"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/metrics"
	"google.golang.org/grpc/metadata"
)

type UserClient struct {
	client    proto_user_v1.UserServiceClient
	conn      *shared_grpc.Client
	clientID  string
	clientKey string
}

func NewUserClient(target string, cfg *config.GRPCConfig, logger *logging.Logger, metrics *metrics.Metrics, clientID, clientKey string) (*UserClient, error) {
	client, err := shared_grpc.NewClient(target, cfg, logger, metrics)
	if err != nil {
		return nil, fmt.Errorf("failed to create shared gRPC client for user service: %w", err)
	}

	return &UserClient{
		client:    proto_user_v1.NewUserServiceClient(client.GetConnection()),
		conn:      client,
		clientID:  clientID,
		clientKey: clientKey,
	}, nil
}

func (c *UserClient) Close() {
	c.conn.Close()
}

func (c *UserClient) GetUserByID(ctx context.Context, userID string) (*proto_user_v1.User, error) {
	req := &proto_user_v1.GetUserRequest{
		UserId: userID,
	}

	md := metadata.New(map[string]string{
		"client-id":  c.clientID,
		"client-key": c.clientKey,
	})
	ctx = metadata.NewOutgoingContext(ctx, md)

	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	resp, err := c.client.GetUser(ctx, req)
	if err != nil {
		return nil, err
	}
	return resp.User, nil
}

// GetUserByEmail retrieves user information by email
func (c *UserClient) GetUserByEmail(ctx context.Context, email string) (*proto_user_v1.User, error) {
	req := &proto_user_v1.GetUserByEmailRequest{
		Email: email,
	}

	md := metadata.New(map[string]string{
		"client-id":  c.clientID,
		"client-key": c.clientKey,
	})
	ctx = metadata.NewOutgoingContext(ctx, md)

	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	resp, err := c.client.GetUserByEmail(ctx, req)
	if err != nil {
		return nil, err
	}
	return resp.User, nil
}
