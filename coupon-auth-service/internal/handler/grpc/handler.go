package handler

import (
	"context"

	"gitlab.zalopay.vn/phunn4/coupon-auth-service/internal/service"
	proto_auth_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/auth/v1"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/errors"
	"google.golang.org/protobuf/types/known/emptypb"
)

type AuthServer struct {
	proto_auth_v1.UnimplementedAuthServiceServer
	svc service.AuthService
}

func NewAuthServer(svc service.AuthService) *AuthServer {
	return &AuthServer{svc: svc}
}

func (s *AuthServer) Login(ctx context.Context, req *proto_auth_v1.LoginRequest) (*proto_auth_v1.LoginResponse, error) {
	resp, err := s.svc.Login(ctx, req)
	if err != nil {
		return nil, errors.ToGRPCError(err)
	}
	return resp, nil
}

func (s *AuthServer) CreateUserCredentialsWithRole(ctx context.Context, req *proto_auth_v1.CreateUserCredentialsWithRoleRequest) (*emptypb.Empty, error) {
	err := s.svc.CreateUserCredentialsWithRole(ctx, req.UserId, req.Email, req.Password, req.RoleName)
	if err != nil {
		return nil, errors.ToGRPCError(err)
	}
	return &emptypb.Empty{}, nil
}

func (s *AuthServer) GetUserRoles(ctx context.Context, req *proto_auth_v1.GetUserRolesRequest) (*proto_auth_v1.GetUserRolesResponse, error) {
	roles, err := s.svc.GetUserRoles(ctx, req.UserId)
	if err != nil {
		return nil, errors.ToGRPCError(err)
	}
	return &proto_auth_v1.GetUserRolesResponse{
		Roles: roles,
	}, nil
}

// UpdateUserEmail method removed - email updates are handled by user service only

func (s *AuthServer) ValidateUserToken(ctx context.Context, req *proto_auth_v1.ValidateUserTokenRequest) (*proto_auth_v1.ValidateUserTokenResponse, error) {
	resp, err := s.svc.ValidateUserToken(ctx, req)
	if err != nil {
		return nil, errors.ToGRPCError(err)
	}
	return resp, nil
}

func (s *AuthServer) RevokeToken(ctx context.Context, req *proto_auth_v1.RevokeTokenRequest) (*proto_auth_v1.RevokeTokenResponse, error) {
	resp, err := s.svc.RevokeToken(ctx, req)
	if err != nil {
		return nil, errors.ToGRPCError(err)
	}
	return resp, nil
}

func (s *AuthServer) RegisterService(ctx context.Context, req *proto_auth_v1.RegisterServiceRequest) (*proto_auth_v1.RegisterServiceResponse, error) {
	resp, err := s.svc.RegisterService(ctx, req)
	if err != nil {
		return nil, errors.ToGRPCError(err)
	}
	return resp, nil
}

func (s *AuthServer) ValidateServiceCredentials(ctx context.Context, req *proto_auth_v1.ValidateServiceCredentialsRequest) (*proto_auth_v1.ValidateServiceCredentialsResponse, error) {
	resp, err := s.svc.ValidateServiceCredentials(ctx, req)
	if err != nil {
		return nil, errors.ToGRPCError(err)
	}
	return resp, nil
}
