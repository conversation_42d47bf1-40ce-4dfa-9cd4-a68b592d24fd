package model

import (
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"time"

	"github.com/google/uuid"
	"golang.org/x/crypto/bcrypt"
)

type UserCredential struct {
	ID           string    `gorm:"type:uuid;primary_key;"`
	UserID       string    `gorm:"type:uuid;not null;uniqueIndex"`
	PasswordHash string    `gorm:"not null"`
	CreatedAt    time.Time `gorm:"not null"`
	UpdatedAt    time.Time `gorm:"not null"`
}

func (UserCredential) TableName() string { return "user_credentials" }

func NewUserCredential(userID, password string) (*UserCredential, error) {
	hash, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return nil, fmt.Errorf("failed to hash password: %w", err)
	}
	return &UserCredential{
		ID:           uuid.NewString(),
		UserID:       userID,
		PasswordHash: string(hash),
	}, nil
}

func (u *UserCredential) CheckPassword(password string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(u.PasswordHash), []byte(password))
	return err == nil
}

type RefreshToken struct {
	ID        string    `gorm:"type:uuid;primary_key;"`
	TokenHash string    `gorm:"type:varchar(255);not null;uniqueIndex"`
	UserID    string    `gorm:"type:uuid;not null"`
	ExpiresAt time.Time `gorm:"not null"`
	IsRevoked bool      `gorm:"not null;default:false"`
}

func (RefreshToken) TableName() string { return "refresh_tokens" }

type ServiceCredential struct {
	ID        string    `gorm:"type:uuid;primary_key;"`
	Name      string    `gorm:"type:varchar(255);not null;unique"`
	ClientID  string    `gorm:"type:varchar(255);not null;unique"`
	ClientKey string    `gorm:"not null"`
	Version   string    `gorm:"type:varchar(50)"`
	CreatedAt time.Time `gorm:"not null"`
	UpdatedAt time.Time `gorm:"not null"`
}

func (ServiceCredential) TableName() string { return "service_credentials" }

func NewServiceCredential(name, version string) (*ServiceCredential, string, error) {
	rawKey, err := generateRandomKey(32)
	if err != nil {
		return nil, "", err
	}

	keyHash, err := hashKey(rawKey)
	if err != nil {
		return nil, "", err
	}

	return &ServiceCredential{
		ID:        uuid.NewString(),
		Name:      name,
		Version:   version,
		ClientID:  uuid.NewString(),
		ClientKey: keyHash,
	}, rawKey, nil
}

type Role struct {
	ID   string `gorm:"type:uuid;primary_key;"`
	Name string `gorm:"type:varchar(100);not null;unique"`
}

func (Role) TableName() string { return "roles" }

// UserRole represents the many-to-many relationship between users and roles
type UserRole struct {
	UserID string `gorm:"type:uuid;not null;primaryKey"`
	RoleID string `gorm:"type:uuid;not null;primaryKey"`
	Role   Role   `gorm:"foreignKey:RoleID;references:ID"`
}

func (UserRole) TableName() string { return "user_roles" }

func generateRandomKey(length int) (string, error) {
	bytes := make([]byte, length)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}

func hashKey(key string) (string, error) {
	bytes, err := bcrypt.GenerateFromPassword([]byte(key), bcrypt.DefaultCost)
	return string(bytes), err
}
