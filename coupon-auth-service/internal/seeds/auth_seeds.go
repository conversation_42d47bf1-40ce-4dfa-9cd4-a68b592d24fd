package seeds

import (
	"github.com/google/uuid"
	"gitlab.zalopay.vn/phunn4/coupon-auth-service/internal/model"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/database"
)

// SeedDefaultRoles creates default roles (ADMIN, USER) if they don't exist
func SeedDefaultRoles(db *database.DB) error {
	roles := []model.Role{
		{
			ID:   uuid.NewString(),
			Name: "ADMIN",
		},
		{
			ID:   uuid.NewString(),
			Name: "USER",
		},
	}

	for _, role := range roles {
		// Check if role already exists
		var existingRole model.Role
		result := db.Where("name = ?", role.Name).First(&existingRole)
		
		if result.Error != nil {
			// Role doesn't exist, create it
			if err := db.Create(&role).Error; err != nil {
				return err
			}
		}
	}

	return nil
}
