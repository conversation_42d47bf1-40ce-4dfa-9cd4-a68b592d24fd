syntax = "proto3";

package coupon.v1;

import "common/v1/common.proto";
import "common/v1/error.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";

option go_package = "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/coupon/v1";

service CouponService {
  // Check whether a coupon can be applied to an order
  rpc CheckEligibility(CouponEligibilityRequest) returns (CouponEligibilityResponse);

  // Suggest automatic coupons the system may apply
  rpc ListEligibleAutoCoupons(AutoCouponEligibilityRequest) returns (ListEligibleCouponsResponse);

  // Update usage count after a successful order
  rpc IncrementUsage(IncrementUsageRequest) returns (google.protobuf.Empty);

  // Health check
  rpc HealthCheck(common.v1.HealthCheckRequest) returns (common.v1.HealthCheckResponse);
}

message CartItem {
  string product_id = 1;
  string category_id = 2;
  int32 quantity = 3;
  double price = 4;
}

message CouponEligibilityRequest {
  common.v1.RequestMetadata metadata = 1;
  string coupon_code = 2;
  string user_id = 3;
  double order_amount = 4;
  google.protobuf.Timestamp order_timestamp = 5;
  repeated CartItem cart_items = 6;
}

message CouponEligibilityResponse {
  common.v1.ResponseMetadata metadata = 1;
  bool eligible = 2;
  string message = 3;
  string coupon_id = 4;
  double discount_amount = 5;
  common.v1.ServiceError error = 6;
}

message AutoCouponEligibilityRequest {
  common.v1.RequestMetadata metadata = 1;
  string user_id = 2;
  double order_amount = 3;
  google.protobuf.Timestamp order_timestamp = 4;
  repeated CartItem cart_items = 5;
}

message Coupon {
  string id = 1;
  string coupon_code = 2;
  double discount_value = 3;
  string usage_method = 4;
  string discount_type_id = 5;
}

message EligibleCoupon {
  bool eligible = 1;
  Coupon coupon = 2;
  double discount_amount = 3;
}

message ListEligibleCouponsResponse {
  common.v1.ResponseMetadata metadata = 1;
  repeated EligibleCoupon coupons = 2;
  common.v1.ServiceError error = 3;
}

message IncrementUsageRequest {
  common.v1.RequestMetadata metadata = 1;
  string coupon_id = 2;
}

