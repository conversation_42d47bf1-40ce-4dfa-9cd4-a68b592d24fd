// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: auth/v1/auth_service.proto

package v1

import (
	v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/common/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type LoginRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *v1.RequestMetadata    `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	Email         string                 `protobuf:"bytes,2,opt,name=email,proto3" json:"email,omitempty"`
	Password      string                 `protobuf:"bytes,3,opt,name=password,proto3" json:"password,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LoginRequest) Reset() {
	*x = LoginRequest{}
	mi := &file_auth_v1_auth_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoginRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginRequest) ProtoMessage() {}

func (x *LoginRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_auth_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginRequest.ProtoReflect.Descriptor instead.
func (*LoginRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_auth_service_proto_rawDescGZIP(), []int{0}
}

func (x *LoginRequest) GetMetadata() *v1.RequestMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *LoginRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *LoginRequest) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

type LoginResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *v1.ResponseMetadata   `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	AccessToken   string                 `protobuf:"bytes,2,opt,name=access_token,json=accessToken,proto3" json:"access_token,omitempty"`
	RefreshToken  string                 `protobuf:"bytes,3,opt,name=refresh_token,json=refreshToken,proto3" json:"refresh_token,omitempty"`
	ExpiresAt     *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=expires_at,json=expiresAt,proto3" json:"expires_at,omitempty"`
	Error         *v1.ServiceError       `protobuf:"bytes,5,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LoginResponse) Reset() {
	*x = LoginResponse{}
	mi := &file_auth_v1_auth_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoginResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginResponse) ProtoMessage() {}

func (x *LoginResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_auth_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginResponse.ProtoReflect.Descriptor instead.
func (*LoginResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_auth_service_proto_rawDescGZIP(), []int{1}
}

func (x *LoginResponse) GetMetadata() *v1.ResponseMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *LoginResponse) GetAccessToken() string {
	if x != nil {
		return x.AccessToken
	}
	return ""
}

func (x *LoginResponse) GetRefreshToken() string {
	if x != nil {
		return x.RefreshToken
	}
	return ""
}

func (x *LoginResponse) GetExpiresAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpiresAt
	}
	return nil
}

func (x *LoginResponse) GetError() *v1.ServiceError {
	if x != nil {
		return x.Error
	}
	return nil
}

type ValidateUserTokenRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *v1.RequestMetadata    `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	Token         string                 `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
	RequiredRole  string                 `protobuf:"bytes,3,opt,name=required_role,json=requiredRole,proto3" json:"required_role,omitempty"`
	Resource      string                 `protobuf:"bytes,4,opt,name=resource,proto3" json:"resource,omitempty"`
	Action        string                 `protobuf:"bytes,5,opt,name=action,proto3" json:"action,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ValidateUserTokenRequest) Reset() {
	*x = ValidateUserTokenRequest{}
	mi := &file_auth_v1_auth_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ValidateUserTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateUserTokenRequest) ProtoMessage() {}

func (x *ValidateUserTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_auth_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateUserTokenRequest.ProtoReflect.Descriptor instead.
func (*ValidateUserTokenRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_auth_service_proto_rawDescGZIP(), []int{2}
}

func (x *ValidateUserTokenRequest) GetMetadata() *v1.RequestMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *ValidateUserTokenRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *ValidateUserTokenRequest) GetRequiredRole() string {
	if x != nil {
		return x.RequiredRole
	}
	return ""
}

func (x *ValidateUserTokenRequest) GetResource() string {
	if x != nil {
		return x.Resource
	}
	return ""
}

func (x *ValidateUserTokenRequest) GetAction() string {
	if x != nil {
		return x.Action
	}
	return ""
}

type ValidateUserTokenResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *v1.ResponseMetadata   `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	Valid         bool                   `protobuf:"varint,2,opt,name=valid,proto3" json:"valid,omitempty"`
	UserId        string                 `protobuf:"bytes,3,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Email         string                 `protobuf:"bytes,4,opt,name=email,proto3" json:"email,omitempty"`
	Roles         []string               `protobuf:"bytes,5,rep,name=roles,proto3" json:"roles,omitempty"`
	Permissions   []string               `protobuf:"bytes,6,rep,name=permissions,proto3" json:"permissions,omitempty"`
	ExpiresAt     *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=expires_at,json=expiresAt,proto3" json:"expires_at,omitempty"`
	Error         *v1.ServiceError       `protobuf:"bytes,8,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ValidateUserTokenResponse) Reset() {
	*x = ValidateUserTokenResponse{}
	mi := &file_auth_v1_auth_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ValidateUserTokenResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateUserTokenResponse) ProtoMessage() {}

func (x *ValidateUserTokenResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_auth_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateUserTokenResponse.ProtoReflect.Descriptor instead.
func (*ValidateUserTokenResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_auth_service_proto_rawDescGZIP(), []int{3}
}

func (x *ValidateUserTokenResponse) GetMetadata() *v1.ResponseMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *ValidateUserTokenResponse) GetValid() bool {
	if x != nil {
		return x.Valid
	}
	return false
}

func (x *ValidateUserTokenResponse) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *ValidateUserTokenResponse) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *ValidateUserTokenResponse) GetRoles() []string {
	if x != nil {
		return x.Roles
	}
	return nil
}

func (x *ValidateUserTokenResponse) GetPermissions() []string {
	if x != nil {
		return x.Permissions
	}
	return nil
}

func (x *ValidateUserTokenResponse) GetExpiresAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpiresAt
	}
	return nil
}

func (x *ValidateUserTokenResponse) GetError() *v1.ServiceError {
	if x != nil {
		return x.Error
	}
	return nil
}

type RevokeTokenRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *v1.RequestMetadata    `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	Token         string                 `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
	TokenType     string                 `protobuf:"bytes,3,opt,name=token_type,json=tokenType,proto3" json:"token_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RevokeTokenRequest) Reset() {
	*x = RevokeTokenRequest{}
	mi := &file_auth_v1_auth_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RevokeTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RevokeTokenRequest) ProtoMessage() {}

func (x *RevokeTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_auth_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RevokeTokenRequest.ProtoReflect.Descriptor instead.
func (*RevokeTokenRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_auth_service_proto_rawDescGZIP(), []int{4}
}

func (x *RevokeTokenRequest) GetMetadata() *v1.RequestMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *RevokeTokenRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *RevokeTokenRequest) GetTokenType() string {
	if x != nil {
		return x.TokenType
	}
	return ""
}

type RevokeTokenResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *v1.ResponseMetadata   `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	Revoked       bool                   `protobuf:"varint,2,opt,name=revoked,proto3" json:"revoked,omitempty"`
	Error         *v1.ServiceError       `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RevokeTokenResponse) Reset() {
	*x = RevokeTokenResponse{}
	mi := &file_auth_v1_auth_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RevokeTokenResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RevokeTokenResponse) ProtoMessage() {}

func (x *RevokeTokenResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_auth_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RevokeTokenResponse.ProtoReflect.Descriptor instead.
func (*RevokeTokenResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_auth_service_proto_rawDescGZIP(), []int{5}
}

func (x *RevokeTokenResponse) GetMetadata() *v1.ResponseMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *RevokeTokenResponse) GetRevoked() bool {
	if x != nil {
		return x.Revoked
	}
	return false
}

func (x *RevokeTokenResponse) GetError() *v1.ServiceError {
	if x != nil {
		return x.Error
	}
	return nil
}

type CreateUserCredentialsWithRoleRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *v1.RequestMetadata    `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	UserId        string                 `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Email         string                 `protobuf:"bytes,3,opt,name=email,proto3" json:"email,omitempty"`
	Password      string                 `protobuf:"bytes,4,opt,name=password,proto3" json:"password,omitempty"`
	RoleName      string                 `protobuf:"bytes,5,opt,name=role_name,json=roleName,proto3" json:"role_name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateUserCredentialsWithRoleRequest) Reset() {
	*x = CreateUserCredentialsWithRoleRequest{}
	mi := &file_auth_v1_auth_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateUserCredentialsWithRoleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateUserCredentialsWithRoleRequest) ProtoMessage() {}

func (x *CreateUserCredentialsWithRoleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_auth_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateUserCredentialsWithRoleRequest.ProtoReflect.Descriptor instead.
func (*CreateUserCredentialsWithRoleRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_auth_service_proto_rawDescGZIP(), []int{6}
}

func (x *CreateUserCredentialsWithRoleRequest) GetMetadata() *v1.RequestMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *CreateUserCredentialsWithRoleRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *CreateUserCredentialsWithRoleRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *CreateUserCredentialsWithRoleRequest) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *CreateUserCredentialsWithRoleRequest) GetRoleName() string {
	if x != nil {
		return x.RoleName
	}
	return ""
}

type GetUserRolesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *v1.RequestMetadata    `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	UserId        string                 `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserRolesRequest) Reset() {
	*x = GetUserRolesRequest{}
	mi := &file_auth_v1_auth_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserRolesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserRolesRequest) ProtoMessage() {}

func (x *GetUserRolesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_auth_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserRolesRequest.ProtoReflect.Descriptor instead.
func (*GetUserRolesRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_auth_service_proto_rawDescGZIP(), []int{7}
}

func (x *GetUserRolesRequest) GetMetadata() *v1.RequestMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *GetUserRolesRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type GetUserRolesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *v1.ResponseMetadata   `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	Roles         []string               `protobuf:"bytes,2,rep,name=roles,proto3" json:"roles,omitempty"`
	Error         *v1.ServiceError       `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserRolesResponse) Reset() {
	*x = GetUserRolesResponse{}
	mi := &file_auth_v1_auth_service_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserRolesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserRolesResponse) ProtoMessage() {}

func (x *GetUserRolesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_auth_service_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserRolesResponse.ProtoReflect.Descriptor instead.
func (*GetUserRolesResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_auth_service_proto_rawDescGZIP(), []int{8}
}

func (x *GetUserRolesResponse) GetMetadata() *v1.ResponseMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *GetUserRolesResponse) GetRoles() []string {
	if x != nil {
		return x.Roles
	}
	return nil
}

func (x *GetUserRolesResponse) GetError() *v1.ServiceError {
	if x != nil {
		return x.Error
	}
	return nil
}

type RegisterServiceRequest struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	Metadata            *v1.RequestMetadata    `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	ServiceName         string                 `protobuf:"bytes,2,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
	ServiceVersion      string                 `protobuf:"bytes,3,opt,name=service_version,json=serviceVersion,proto3" json:"service_version,omitempty"`
	Description         string                 `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	RequiredPermissions []string               `protobuf:"bytes,5,rep,name=required_permissions,json=requiredPermissions,proto3" json:"required_permissions,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *RegisterServiceRequest) Reset() {
	*x = RegisterServiceRequest{}
	mi := &file_auth_v1_auth_service_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegisterServiceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterServiceRequest) ProtoMessage() {}

func (x *RegisterServiceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_auth_service_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterServiceRequest.ProtoReflect.Descriptor instead.
func (*RegisterServiceRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_auth_service_proto_rawDescGZIP(), []int{9}
}

func (x *RegisterServiceRequest) GetMetadata() *v1.RequestMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *RegisterServiceRequest) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *RegisterServiceRequest) GetServiceVersion() string {
	if x != nil {
		return x.ServiceVersion
	}
	return ""
}

func (x *RegisterServiceRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *RegisterServiceRequest) GetRequiredPermissions() []string {
	if x != nil {
		return x.RequiredPermissions
	}
	return nil
}

type RegisterServiceResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *v1.ResponseMetadata   `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	ServiceId     string                 `protobuf:"bytes,2,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	ClientId      string                 `protobuf:"bytes,3,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	ClientKey     string                 `protobuf:"bytes,4,opt,name=client_key,json=clientKey,proto3" json:"client_key,omitempty"`
	ExpiresAt     *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=expires_at,json=expiresAt,proto3" json:"expires_at,omitempty"`
	Error         *v1.ServiceError       `protobuf:"bytes,6,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RegisterServiceResponse) Reset() {
	*x = RegisterServiceResponse{}
	mi := &file_auth_v1_auth_service_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegisterServiceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterServiceResponse) ProtoMessage() {}

func (x *RegisterServiceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_auth_service_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterServiceResponse.ProtoReflect.Descriptor instead.
func (*RegisterServiceResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_auth_service_proto_rawDescGZIP(), []int{10}
}

func (x *RegisterServiceResponse) GetMetadata() *v1.ResponseMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *RegisterServiceResponse) GetServiceId() string {
	if x != nil {
		return x.ServiceId
	}
	return ""
}

func (x *RegisterServiceResponse) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *RegisterServiceResponse) GetClientKey() string {
	if x != nil {
		return x.ClientKey
	}
	return ""
}

func (x *RegisterServiceResponse) GetExpiresAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpiresAt
	}
	return nil
}

func (x *RegisterServiceResponse) GetError() *v1.ServiceError {
	if x != nil {
		return x.Error
	}
	return nil
}

type ValidateServiceCredentialsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *v1.RequestMetadata    `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	ClientId      string                 `protobuf:"bytes,2,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	ClientKey     string                 `protobuf:"bytes,3,opt,name=client_key,json=clientKey,proto3" json:"client_key,omitempty"`
	ServiceName   string                 `protobuf:"bytes,4,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ValidateServiceCredentialsRequest) Reset() {
	*x = ValidateServiceCredentialsRequest{}
	mi := &file_auth_v1_auth_service_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ValidateServiceCredentialsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateServiceCredentialsRequest) ProtoMessage() {}

func (x *ValidateServiceCredentialsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_auth_service_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateServiceCredentialsRequest.ProtoReflect.Descriptor instead.
func (*ValidateServiceCredentialsRequest) Descriptor() ([]byte, []int) {
	return file_auth_v1_auth_service_proto_rawDescGZIP(), []int{11}
}

func (x *ValidateServiceCredentialsRequest) GetMetadata() *v1.RequestMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *ValidateServiceCredentialsRequest) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *ValidateServiceCredentialsRequest) GetClientKey() string {
	if x != nil {
		return x.ClientKey
	}
	return ""
}

func (x *ValidateServiceCredentialsRequest) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

type ValidateServiceCredentialsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *v1.ResponseMetadata   `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	Valid         bool                   `protobuf:"varint,2,opt,name=valid,proto3" json:"valid,omitempty"`
	ServiceId     string                 `protobuf:"bytes,3,opt,name=service_id,json=serviceId,proto3" json:"service_id,omitempty"`
	ServiceName   string                 `protobuf:"bytes,4,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
	Permissions   []string               `protobuf:"bytes,5,rep,name=permissions,proto3" json:"permissions,omitempty"`
	Error         *v1.ServiceError       `protobuf:"bytes,6,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ValidateServiceCredentialsResponse) Reset() {
	*x = ValidateServiceCredentialsResponse{}
	mi := &file_auth_v1_auth_service_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ValidateServiceCredentialsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateServiceCredentialsResponse) ProtoMessage() {}

func (x *ValidateServiceCredentialsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_auth_v1_auth_service_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateServiceCredentialsResponse.ProtoReflect.Descriptor instead.
func (*ValidateServiceCredentialsResponse) Descriptor() ([]byte, []int) {
	return file_auth_v1_auth_service_proto_rawDescGZIP(), []int{12}
}

func (x *ValidateServiceCredentialsResponse) GetMetadata() *v1.ResponseMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *ValidateServiceCredentialsResponse) GetValid() bool {
	if x != nil {
		return x.Valid
	}
	return false
}

func (x *ValidateServiceCredentialsResponse) GetServiceId() string {
	if x != nil {
		return x.ServiceId
	}
	return ""
}

func (x *ValidateServiceCredentialsResponse) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *ValidateServiceCredentialsResponse) GetPermissions() []string {
	if x != nil {
		return x.Permissions
	}
	return nil
}

func (x *ValidateServiceCredentialsResponse) GetError() *v1.ServiceError {
	if x != nil {
		return x.Error
	}
	return nil
}

var File_auth_v1_auth_service_proto protoreflect.FileDescriptor

const file_auth_v1_auth_service_proto_rawDesc = "" +
	"\n" +
	"\x1aauth/v1/auth_service.proto\x12\aauth.v1\x1a\x16common/v1/common.proto\x1a\x15common/v1/error.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"x\n" +
	"\fLoginRequest\x126\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1a.common.v1.RequestMetadataR\bmetadata\x12\x14\n" +
	"\x05email\x18\x02 \x01(\tR\x05email\x12\x1a\n" +
	"\bpassword\x18\x03 \x01(\tR\bpassword\"\xfa\x01\n" +
	"\rLoginResponse\x127\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1b.common.v1.ResponseMetadataR\bmetadata\x12!\n" +
	"\faccess_token\x18\x02 \x01(\tR\vaccessToken\x12#\n" +
	"\rrefresh_token\x18\x03 \x01(\tR\frefreshToken\x129\n" +
	"\n" +
	"expires_at\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\texpiresAt\x12-\n" +
	"\x05error\x18\x05 \x01(\v2\x17.common.v1.ServiceErrorR\x05error\"\xc1\x01\n" +
	"\x18ValidateUserTokenRequest\x126\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1a.common.v1.RequestMetadataR\bmetadata\x12\x14\n" +
	"\x05token\x18\x02 \x01(\tR\x05token\x12#\n" +
	"\rrequired_role\x18\x03 \x01(\tR\frequiredRole\x12\x1a\n" +
	"\bresource\x18\x04 \x01(\tR\bresource\x12\x16\n" +
	"\x06action\x18\x05 \x01(\tR\x06action\"\xbb\x02\n" +
	"\x19ValidateUserTokenResponse\x127\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1b.common.v1.ResponseMetadataR\bmetadata\x12\x14\n" +
	"\x05valid\x18\x02 \x01(\bR\x05valid\x12\x17\n" +
	"\auser_id\x18\x03 \x01(\tR\x06userId\x12\x14\n" +
	"\x05email\x18\x04 \x01(\tR\x05email\x12\x14\n" +
	"\x05roles\x18\x05 \x03(\tR\x05roles\x12 \n" +
	"\vpermissions\x18\x06 \x03(\tR\vpermissions\x129\n" +
	"\n" +
	"expires_at\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\texpiresAt\x12-\n" +
	"\x05error\x18\b \x01(\v2\x17.common.v1.ServiceErrorR\x05error\"\x81\x01\n" +
	"\x12RevokeTokenRequest\x126\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1a.common.v1.RequestMetadataR\bmetadata\x12\x14\n" +
	"\x05token\x18\x02 \x01(\tR\x05token\x12\x1d\n" +
	"\n" +
	"token_type\x18\x03 \x01(\tR\ttokenType\"\x97\x01\n" +
	"\x13RevokeTokenResponse\x127\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1b.common.v1.ResponseMetadataR\bmetadata\x12\x18\n" +
	"\arevoked\x18\x02 \x01(\bR\arevoked\x12-\n" +
	"\x05error\x18\x03 \x01(\v2\x17.common.v1.ServiceErrorR\x05error\"\xc6\x01\n" +
	"$CreateUserCredentialsWithRoleRequest\x126\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1a.common.v1.RequestMetadataR\bmetadata\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\tR\x06userId\x12\x14\n" +
	"\x05email\x18\x03 \x01(\tR\x05email\x12\x1a\n" +
	"\bpassword\x18\x04 \x01(\tR\bpassword\x12\x1b\n" +
	"\trole_name\x18\x05 \x01(\tR\broleName\"f\n" +
	"\x13GetUserRolesRequest\x126\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1a.common.v1.RequestMetadataR\bmetadata\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\tR\x06userId\"\x94\x01\n" +
	"\x14GetUserRolesResponse\x127\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1b.common.v1.ResponseMetadataR\bmetadata\x12\x14\n" +
	"\x05roles\x18\x02 \x03(\tR\x05roles\x12-\n" +
	"\x05error\x18\x03 \x01(\v2\x17.common.v1.ServiceErrorR\x05error\"\xf1\x01\n" +
	"\x16RegisterServiceRequest\x126\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1a.common.v1.RequestMetadataR\bmetadata\x12!\n" +
	"\fservice_name\x18\x02 \x01(\tR\vserviceName\x12'\n" +
	"\x0fservice_version\x18\x03 \x01(\tR\x0eserviceVersion\x12 \n" +
	"\vdescription\x18\x04 \x01(\tR\vdescription\x121\n" +
	"\x14required_permissions\x18\x05 \x03(\tR\x13requiredPermissions\"\x97\x02\n" +
	"\x17RegisterServiceResponse\x127\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1b.common.v1.ResponseMetadataR\bmetadata\x12\x1d\n" +
	"\n" +
	"service_id\x18\x02 \x01(\tR\tserviceId\x12\x1b\n" +
	"\tclient_id\x18\x03 \x01(\tR\bclientId\x12\x1d\n" +
	"\n" +
	"client_key\x18\x04 \x01(\tR\tclientKey\x129\n" +
	"\n" +
	"expires_at\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\texpiresAt\x12-\n" +
	"\x05error\x18\x06 \x01(\v2\x17.common.v1.ServiceErrorR\x05error\"\xba\x01\n" +
	"!ValidateServiceCredentialsRequest\x126\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1a.common.v1.RequestMetadataR\bmetadata\x12\x1b\n" +
	"\tclient_id\x18\x02 \x01(\tR\bclientId\x12\x1d\n" +
	"\n" +
	"client_key\x18\x03 \x01(\tR\tclientKey\x12!\n" +
	"\fservice_name\x18\x04 \x01(\tR\vserviceName\"\x86\x02\n" +
	"\"ValidateServiceCredentialsResponse\x127\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1b.common.v1.ResponseMetadataR\bmetadata\x12\x14\n" +
	"\x05valid\x18\x02 \x01(\bR\x05valid\x12\x1d\n" +
	"\n" +
	"service_id\x18\x03 \x01(\tR\tserviceId\x12!\n" +
	"\fservice_name\x18\x04 \x01(\tR\vserviceName\x12 \n" +
	"\vpermissions\x18\x05 \x03(\tR\vpermissions\x12-\n" +
	"\x05error\x18\x06 \x01(\v2\x17.common.v1.ServiceErrorR\x05error2\xbb\x05\n" +
	"\vAuthService\x126\n" +
	"\x05Login\x12\x15.auth.v1.LoginRequest\x1a\x16.auth.v1.LoginResponse\x12L\n" +
	"\vHealthCheck\x12\x1d.common.v1.HealthCheckRequest\x1a\x1e.common.v1.HealthCheckResponse\x12Z\n" +
	"\x11ValidateUserToken\x12!.auth.v1.ValidateUserTokenRequest\x1a\".auth.v1.ValidateUserTokenResponse\x12H\n" +
	"\vRevokeToken\x12\x1b.auth.v1.RevokeTokenRequest\x1a\x1c.auth.v1.RevokeTokenResponse\x12f\n" +
	"\x1dCreateUserCredentialsWithRole\x12-.auth.v1.CreateUserCredentialsWithRoleRequest\x1a\x16.google.protobuf.Empty\x12K\n" +
	"\fGetUserRoles\x12\x1c.auth.v1.GetUserRolesRequest\x1a\x1d.auth.v1.GetUserRolesResponse\x12T\n" +
	"\x0fRegisterService\x12\x1f.auth.v1.RegisterServiceRequest\x1a .auth.v1.RegisterServiceResponse\x12u\n" +
	"\x1aValidateServiceCredentials\x12*.auth.v1.ValidateServiceCredentialsRequest\x1a+.auth.v1.ValidateServiceCredentialsResponseB6Z4gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/auth/v1b\x06proto3"

var (
	file_auth_v1_auth_service_proto_rawDescOnce sync.Once
	file_auth_v1_auth_service_proto_rawDescData []byte
)

func file_auth_v1_auth_service_proto_rawDescGZIP() []byte {
	file_auth_v1_auth_service_proto_rawDescOnce.Do(func() {
		file_auth_v1_auth_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_auth_v1_auth_service_proto_rawDesc), len(file_auth_v1_auth_service_proto_rawDesc)))
	})
	return file_auth_v1_auth_service_proto_rawDescData
}

var file_auth_v1_auth_service_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_auth_v1_auth_service_proto_goTypes = []any{
	(*LoginRequest)(nil),                         // 0: auth.v1.LoginRequest
	(*LoginResponse)(nil),                        // 1: auth.v1.LoginResponse
	(*ValidateUserTokenRequest)(nil),             // 2: auth.v1.ValidateUserTokenRequest
	(*ValidateUserTokenResponse)(nil),            // 3: auth.v1.ValidateUserTokenResponse
	(*RevokeTokenRequest)(nil),                   // 4: auth.v1.RevokeTokenRequest
	(*RevokeTokenResponse)(nil),                  // 5: auth.v1.RevokeTokenResponse
	(*CreateUserCredentialsWithRoleRequest)(nil), // 6: auth.v1.CreateUserCredentialsWithRoleRequest
	(*GetUserRolesRequest)(nil),                  // 7: auth.v1.GetUserRolesRequest
	(*GetUserRolesResponse)(nil),                 // 8: auth.v1.GetUserRolesResponse
	(*RegisterServiceRequest)(nil),               // 9: auth.v1.RegisterServiceRequest
	(*RegisterServiceResponse)(nil),              // 10: auth.v1.RegisterServiceResponse
	(*ValidateServiceCredentialsRequest)(nil),    // 11: auth.v1.ValidateServiceCredentialsRequest
	(*ValidateServiceCredentialsResponse)(nil),   // 12: auth.v1.ValidateServiceCredentialsResponse
	(*v1.RequestMetadata)(nil),                   // 13: common.v1.RequestMetadata
	(*v1.ResponseMetadata)(nil),                  // 14: common.v1.ResponseMetadata
	(*timestamppb.Timestamp)(nil),                // 15: google.protobuf.Timestamp
	(*v1.ServiceError)(nil),                      // 16: common.v1.ServiceError
	(*v1.HealthCheckRequest)(nil),                // 17: common.v1.HealthCheckRequest
	(*v1.HealthCheckResponse)(nil),               // 18: common.v1.HealthCheckResponse
	(*emptypb.Empty)(nil),                        // 19: google.protobuf.Empty
}
var file_auth_v1_auth_service_proto_depIdxs = []int32{
	13, // 0: auth.v1.LoginRequest.metadata:type_name -> common.v1.RequestMetadata
	14, // 1: auth.v1.LoginResponse.metadata:type_name -> common.v1.ResponseMetadata
	15, // 2: auth.v1.LoginResponse.expires_at:type_name -> google.protobuf.Timestamp
	16, // 3: auth.v1.LoginResponse.error:type_name -> common.v1.ServiceError
	13, // 4: auth.v1.ValidateUserTokenRequest.metadata:type_name -> common.v1.RequestMetadata
	14, // 5: auth.v1.ValidateUserTokenResponse.metadata:type_name -> common.v1.ResponseMetadata
	15, // 6: auth.v1.ValidateUserTokenResponse.expires_at:type_name -> google.protobuf.Timestamp
	16, // 7: auth.v1.ValidateUserTokenResponse.error:type_name -> common.v1.ServiceError
	13, // 8: auth.v1.RevokeTokenRequest.metadata:type_name -> common.v1.RequestMetadata
	14, // 9: auth.v1.RevokeTokenResponse.metadata:type_name -> common.v1.ResponseMetadata
	16, // 10: auth.v1.RevokeTokenResponse.error:type_name -> common.v1.ServiceError
	13, // 11: auth.v1.CreateUserCredentialsWithRoleRequest.metadata:type_name -> common.v1.RequestMetadata
	13, // 12: auth.v1.GetUserRolesRequest.metadata:type_name -> common.v1.RequestMetadata
	14, // 13: auth.v1.GetUserRolesResponse.metadata:type_name -> common.v1.ResponseMetadata
	16, // 14: auth.v1.GetUserRolesResponse.error:type_name -> common.v1.ServiceError
	13, // 15: auth.v1.RegisterServiceRequest.metadata:type_name -> common.v1.RequestMetadata
	14, // 16: auth.v1.RegisterServiceResponse.metadata:type_name -> common.v1.ResponseMetadata
	15, // 17: auth.v1.RegisterServiceResponse.expires_at:type_name -> google.protobuf.Timestamp
	16, // 18: auth.v1.RegisterServiceResponse.error:type_name -> common.v1.ServiceError
	13, // 19: auth.v1.ValidateServiceCredentialsRequest.metadata:type_name -> common.v1.RequestMetadata
	14, // 20: auth.v1.ValidateServiceCredentialsResponse.metadata:type_name -> common.v1.ResponseMetadata
	16, // 21: auth.v1.ValidateServiceCredentialsResponse.error:type_name -> common.v1.ServiceError
	0,  // 22: auth.v1.AuthService.Login:input_type -> auth.v1.LoginRequest
	17, // 23: auth.v1.AuthService.HealthCheck:input_type -> common.v1.HealthCheckRequest
	2,  // 24: auth.v1.AuthService.ValidateUserToken:input_type -> auth.v1.ValidateUserTokenRequest
	4,  // 25: auth.v1.AuthService.RevokeToken:input_type -> auth.v1.RevokeTokenRequest
	6,  // 26: auth.v1.AuthService.CreateUserCredentialsWithRole:input_type -> auth.v1.CreateUserCredentialsWithRoleRequest
	7,  // 27: auth.v1.AuthService.GetUserRoles:input_type -> auth.v1.GetUserRolesRequest
	9,  // 28: auth.v1.AuthService.RegisterService:input_type -> auth.v1.RegisterServiceRequest
	11, // 29: auth.v1.AuthService.ValidateServiceCredentials:input_type -> auth.v1.ValidateServiceCredentialsRequest
	1,  // 30: auth.v1.AuthService.Login:output_type -> auth.v1.LoginResponse
	18, // 31: auth.v1.AuthService.HealthCheck:output_type -> common.v1.HealthCheckResponse
	3,  // 32: auth.v1.AuthService.ValidateUserToken:output_type -> auth.v1.ValidateUserTokenResponse
	5,  // 33: auth.v1.AuthService.RevokeToken:output_type -> auth.v1.RevokeTokenResponse
	19, // 34: auth.v1.AuthService.CreateUserCredentialsWithRole:output_type -> google.protobuf.Empty
	8,  // 35: auth.v1.AuthService.GetUserRoles:output_type -> auth.v1.GetUserRolesResponse
	10, // 36: auth.v1.AuthService.RegisterService:output_type -> auth.v1.RegisterServiceResponse
	12, // 37: auth.v1.AuthService.ValidateServiceCredentials:output_type -> auth.v1.ValidateServiceCredentialsResponse
	30, // [30:38] is the sub-list for method output_type
	22, // [22:30] is the sub-list for method input_type
	22, // [22:22] is the sub-list for extension type_name
	22, // [22:22] is the sub-list for extension extendee
	0,  // [0:22] is the sub-list for field type_name
}

func init() { file_auth_v1_auth_service_proto_init() }
func file_auth_v1_auth_service_proto_init() {
	if File_auth_v1_auth_service_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_auth_v1_auth_service_proto_rawDesc), len(file_auth_v1_auth_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_auth_v1_auth_service_proto_goTypes,
		DependencyIndexes: file_auth_v1_auth_service_proto_depIdxs,
		MessageInfos:      file_auth_v1_auth_service_proto_msgTypes,
	}.Build()
	File_auth_v1_auth_service_proto = out.File
	file_auth_v1_auth_service_proto_goTypes = nil
	file_auth_v1_auth_service_proto_depIdxs = nil
}
