// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: coupon/v1/coupon_service.proto

package v1

import (
	v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/common/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CartItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ProductId     string                 `protobuf:"bytes,1,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"`
	CategoryId    string                 `protobuf:"bytes,2,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	Quantity      int32                  `protobuf:"varint,3,opt,name=quantity,proto3" json:"quantity,omitempty"`
	Price         float64                `protobuf:"fixed64,4,opt,name=price,proto3" json:"price,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CartItem) Reset() {
	*x = CartItem{}
	mi := &file_coupon_v1_coupon_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CartItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CartItem) ProtoMessage() {}

func (x *CartItem) ProtoReflect() protoreflect.Message {
	mi := &file_coupon_v1_coupon_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CartItem.ProtoReflect.Descriptor instead.
func (*CartItem) Descriptor() ([]byte, []int) {
	return file_coupon_v1_coupon_service_proto_rawDescGZIP(), []int{0}
}

func (x *CartItem) GetProductId() string {
	if x != nil {
		return x.ProductId
	}
	return ""
}

func (x *CartItem) GetCategoryId() string {
	if x != nil {
		return x.CategoryId
	}
	return ""
}

func (x *CartItem) GetQuantity() int32 {
	if x != nil {
		return x.Quantity
	}
	return 0
}

func (x *CartItem) GetPrice() float64 {
	if x != nil {
		return x.Price
	}
	return 0
}

type CouponEligibilityRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Metadata       *v1.RequestMetadata    `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	CouponCode     string                 `protobuf:"bytes,2,opt,name=coupon_code,json=couponCode,proto3" json:"coupon_code,omitempty"`
	UserId         string                 `protobuf:"bytes,3,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	OrderAmount    float64                `protobuf:"fixed64,4,opt,name=order_amount,json=orderAmount,proto3" json:"order_amount,omitempty"`
	OrderTimestamp *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=order_timestamp,json=orderTimestamp,proto3" json:"order_timestamp,omitempty"`
	CartItems      []*CartItem            `protobuf:"bytes,6,rep,name=cart_items,json=cartItems,proto3" json:"cart_items,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CouponEligibilityRequest) Reset() {
	*x = CouponEligibilityRequest{}
	mi := &file_coupon_v1_coupon_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CouponEligibilityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CouponEligibilityRequest) ProtoMessage() {}

func (x *CouponEligibilityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_coupon_v1_coupon_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CouponEligibilityRequest.ProtoReflect.Descriptor instead.
func (*CouponEligibilityRequest) Descriptor() ([]byte, []int) {
	return file_coupon_v1_coupon_service_proto_rawDescGZIP(), []int{1}
}

func (x *CouponEligibilityRequest) GetMetadata() *v1.RequestMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *CouponEligibilityRequest) GetCouponCode() string {
	if x != nil {
		return x.CouponCode
	}
	return ""
}

func (x *CouponEligibilityRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *CouponEligibilityRequest) GetOrderAmount() float64 {
	if x != nil {
		return x.OrderAmount
	}
	return 0
}

func (x *CouponEligibilityRequest) GetOrderTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.OrderTimestamp
	}
	return nil
}

func (x *CouponEligibilityRequest) GetCartItems() []*CartItem {
	if x != nil {
		return x.CartItems
	}
	return nil
}

type CouponEligibilityResponse struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Metadata       *v1.ResponseMetadata   `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	Eligible       bool                   `protobuf:"varint,2,opt,name=eligible,proto3" json:"eligible,omitempty"`
	Message        string                 `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`
	CouponId       string                 `protobuf:"bytes,4,opt,name=coupon_id,json=couponId,proto3" json:"coupon_id,omitempty"`
	DiscountAmount float64                `protobuf:"fixed64,5,opt,name=discount_amount,json=discountAmount,proto3" json:"discount_amount,omitempty"`
	Error          *v1.ServiceError       `protobuf:"bytes,6,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CouponEligibilityResponse) Reset() {
	*x = CouponEligibilityResponse{}
	mi := &file_coupon_v1_coupon_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CouponEligibilityResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CouponEligibilityResponse) ProtoMessage() {}

func (x *CouponEligibilityResponse) ProtoReflect() protoreflect.Message {
	mi := &file_coupon_v1_coupon_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CouponEligibilityResponse.ProtoReflect.Descriptor instead.
func (*CouponEligibilityResponse) Descriptor() ([]byte, []int) {
	return file_coupon_v1_coupon_service_proto_rawDescGZIP(), []int{2}
}

func (x *CouponEligibilityResponse) GetMetadata() *v1.ResponseMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *CouponEligibilityResponse) GetEligible() bool {
	if x != nil {
		return x.Eligible
	}
	return false
}

func (x *CouponEligibilityResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *CouponEligibilityResponse) GetCouponId() string {
	if x != nil {
		return x.CouponId
	}
	return ""
}

func (x *CouponEligibilityResponse) GetDiscountAmount() float64 {
	if x != nil {
		return x.DiscountAmount
	}
	return 0
}

func (x *CouponEligibilityResponse) GetError() *v1.ServiceError {
	if x != nil {
		return x.Error
	}
	return nil
}

type AutoCouponEligibilityRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Metadata       *v1.RequestMetadata    `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	UserId         string                 `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	OrderAmount    float64                `protobuf:"fixed64,3,opt,name=order_amount,json=orderAmount,proto3" json:"order_amount,omitempty"`
	OrderTimestamp *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=order_timestamp,json=orderTimestamp,proto3" json:"order_timestamp,omitempty"`
	CartItems      []*CartItem            `protobuf:"bytes,5,rep,name=cart_items,json=cartItems,proto3" json:"cart_items,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *AutoCouponEligibilityRequest) Reset() {
	*x = AutoCouponEligibilityRequest{}
	mi := &file_coupon_v1_coupon_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AutoCouponEligibilityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AutoCouponEligibilityRequest) ProtoMessage() {}

func (x *AutoCouponEligibilityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_coupon_v1_coupon_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AutoCouponEligibilityRequest.ProtoReflect.Descriptor instead.
func (*AutoCouponEligibilityRequest) Descriptor() ([]byte, []int) {
	return file_coupon_v1_coupon_service_proto_rawDescGZIP(), []int{3}
}

func (x *AutoCouponEligibilityRequest) GetMetadata() *v1.RequestMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *AutoCouponEligibilityRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *AutoCouponEligibilityRequest) GetOrderAmount() float64 {
	if x != nil {
		return x.OrderAmount
	}
	return 0
}

func (x *AutoCouponEligibilityRequest) GetOrderTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.OrderTimestamp
	}
	return nil
}

func (x *AutoCouponEligibilityRequest) GetCartItems() []*CartItem {
	if x != nil {
		return x.CartItems
	}
	return nil
}

type Coupon struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Id             string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	CouponCode     string                 `protobuf:"bytes,2,opt,name=coupon_code,json=couponCode,proto3" json:"coupon_code,omitempty"`
	DiscountValue  float64                `protobuf:"fixed64,3,opt,name=discount_value,json=discountValue,proto3" json:"discount_value,omitempty"`
	UsageMethod    string                 `protobuf:"bytes,4,opt,name=usage_method,json=usageMethod,proto3" json:"usage_method,omitempty"`
	DiscountTypeId string                 `protobuf:"bytes,5,opt,name=discount_type_id,json=discountTypeId,proto3" json:"discount_type_id,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *Coupon) Reset() {
	*x = Coupon{}
	mi := &file_coupon_v1_coupon_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Coupon) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Coupon) ProtoMessage() {}

func (x *Coupon) ProtoReflect() protoreflect.Message {
	mi := &file_coupon_v1_coupon_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Coupon.ProtoReflect.Descriptor instead.
func (*Coupon) Descriptor() ([]byte, []int) {
	return file_coupon_v1_coupon_service_proto_rawDescGZIP(), []int{4}
}

func (x *Coupon) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Coupon) GetCouponCode() string {
	if x != nil {
		return x.CouponCode
	}
	return ""
}

func (x *Coupon) GetDiscountValue() float64 {
	if x != nil {
		return x.DiscountValue
	}
	return 0
}

func (x *Coupon) GetUsageMethod() string {
	if x != nil {
		return x.UsageMethod
	}
	return ""
}

func (x *Coupon) GetDiscountTypeId() string {
	if x != nil {
		return x.DiscountTypeId
	}
	return ""
}

type EligibleCoupon struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Eligible       bool                   `protobuf:"varint,1,opt,name=eligible,proto3" json:"eligible,omitempty"`
	Coupon         *Coupon                `protobuf:"bytes,2,opt,name=coupon,proto3" json:"coupon,omitempty"`
	DiscountAmount float64                `protobuf:"fixed64,3,opt,name=discount_amount,json=discountAmount,proto3" json:"discount_amount,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *EligibleCoupon) Reset() {
	*x = EligibleCoupon{}
	mi := &file_coupon_v1_coupon_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EligibleCoupon) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EligibleCoupon) ProtoMessage() {}

func (x *EligibleCoupon) ProtoReflect() protoreflect.Message {
	mi := &file_coupon_v1_coupon_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EligibleCoupon.ProtoReflect.Descriptor instead.
func (*EligibleCoupon) Descriptor() ([]byte, []int) {
	return file_coupon_v1_coupon_service_proto_rawDescGZIP(), []int{5}
}

func (x *EligibleCoupon) GetEligible() bool {
	if x != nil {
		return x.Eligible
	}
	return false
}

func (x *EligibleCoupon) GetCoupon() *Coupon {
	if x != nil {
		return x.Coupon
	}
	return nil
}

func (x *EligibleCoupon) GetDiscountAmount() float64 {
	if x != nil {
		return x.DiscountAmount
	}
	return 0
}

type ListEligibleCouponsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *v1.ResponseMetadata   `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	Coupons       []*EligibleCoupon      `protobuf:"bytes,2,rep,name=coupons,proto3" json:"coupons,omitempty"`
	Error         *v1.ServiceError       `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListEligibleCouponsResponse) Reset() {
	*x = ListEligibleCouponsResponse{}
	mi := &file_coupon_v1_coupon_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListEligibleCouponsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListEligibleCouponsResponse) ProtoMessage() {}

func (x *ListEligibleCouponsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_coupon_v1_coupon_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListEligibleCouponsResponse.ProtoReflect.Descriptor instead.
func (*ListEligibleCouponsResponse) Descriptor() ([]byte, []int) {
	return file_coupon_v1_coupon_service_proto_rawDescGZIP(), []int{6}
}

func (x *ListEligibleCouponsResponse) GetMetadata() *v1.ResponseMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *ListEligibleCouponsResponse) GetCoupons() []*EligibleCoupon {
	if x != nil {
		return x.Coupons
	}
	return nil
}

func (x *ListEligibleCouponsResponse) GetError() *v1.ServiceError {
	if x != nil {
		return x.Error
	}
	return nil
}

type IncrementUsageRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *v1.RequestMetadata    `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	CouponId      string                 `protobuf:"bytes,2,opt,name=coupon_id,json=couponId,proto3" json:"coupon_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *IncrementUsageRequest) Reset() {
	*x = IncrementUsageRequest{}
	mi := &file_coupon_v1_coupon_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IncrementUsageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IncrementUsageRequest) ProtoMessage() {}

func (x *IncrementUsageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_coupon_v1_coupon_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IncrementUsageRequest.ProtoReflect.Descriptor instead.
func (*IncrementUsageRequest) Descriptor() ([]byte, []int) {
	return file_coupon_v1_coupon_service_proto_rawDescGZIP(), []int{7}
}

func (x *IncrementUsageRequest) GetMetadata() *v1.RequestMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *IncrementUsageRequest) GetCouponId() string {
	if x != nil {
		return x.CouponId
	}
	return ""
}

var File_coupon_v1_coupon_service_proto protoreflect.FileDescriptor

const file_coupon_v1_coupon_service_proto_rawDesc = "" +
	"\n" +
	"\x1ecoupon/v1/coupon_service.proto\x12\tcoupon.v1\x1a\x16common/v1/common.proto\x1a\x15common/v1/error.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"|\n" +
	"\bCartItem\x12\x1d\n" +
	"\n" +
	"product_id\x18\x01 \x01(\tR\tproductId\x12\x1f\n" +
	"\vcategory_id\x18\x02 \x01(\tR\n" +
	"categoryId\x12\x1a\n" +
	"\bquantity\x18\x03 \x01(\x05R\bquantity\x12\x14\n" +
	"\x05price\x18\x04 \x01(\x01R\x05price\"\xa8\x02\n" +
	"\x18CouponEligibilityRequest\x126\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1a.common.v1.RequestMetadataR\bmetadata\x12\x1f\n" +
	"\vcoupon_code\x18\x02 \x01(\tR\n" +
	"couponCode\x12\x17\n" +
	"\auser_id\x18\x03 \x01(\tR\x06userId\x12!\n" +
	"\forder_amount\x18\x04 \x01(\x01R\vorderAmount\x12C\n" +
	"\x0forder_timestamp\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\x0eorderTimestamp\x122\n" +
	"\n" +
	"cart_items\x18\x06 \x03(\v2\x13.coupon.v1.CartItemR\tcartItems\"\xff\x01\n" +
	"\x19CouponEligibilityResponse\x127\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1b.common.v1.ResponseMetadataR\bmetadata\x12\x1a\n" +
	"\beligible\x18\x02 \x01(\bR\beligible\x12\x18\n" +
	"\amessage\x18\x03 \x01(\tR\amessage\x12\x1b\n" +
	"\tcoupon_id\x18\x04 \x01(\tR\bcouponId\x12'\n" +
	"\x0fdiscount_amount\x18\x05 \x01(\x01R\x0ediscountAmount\x12-\n" +
	"\x05error\x18\x06 \x01(\v2\x17.common.v1.ServiceErrorR\x05error\"\x8b\x02\n" +
	"\x1cAutoCouponEligibilityRequest\x126\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1a.common.v1.RequestMetadataR\bmetadata\x12\x17\n" +
	"\auser_id\x18\x02 \x01(\tR\x06userId\x12!\n" +
	"\forder_amount\x18\x03 \x01(\x01R\vorderAmount\x12C\n" +
	"\x0forder_timestamp\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\x0eorderTimestamp\x122\n" +
	"\n" +
	"cart_items\x18\x05 \x03(\v2\x13.coupon.v1.CartItemR\tcartItems\"\xad\x01\n" +
	"\x06Coupon\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x1f\n" +
	"\vcoupon_code\x18\x02 \x01(\tR\n" +
	"couponCode\x12%\n" +
	"\x0ediscount_value\x18\x03 \x01(\x01R\rdiscountValue\x12!\n" +
	"\fusage_method\x18\x04 \x01(\tR\vusageMethod\x12(\n" +
	"\x10discount_type_id\x18\x05 \x01(\tR\x0ediscountTypeId\"\x80\x01\n" +
	"\x0eEligibleCoupon\x12\x1a\n" +
	"\beligible\x18\x01 \x01(\bR\beligible\x12)\n" +
	"\x06coupon\x18\x02 \x01(\v2\x11.coupon.v1.CouponR\x06coupon\x12'\n" +
	"\x0fdiscount_amount\x18\x03 \x01(\x01R\x0ediscountAmount\"\xba\x01\n" +
	"\x1bListEligibleCouponsResponse\x127\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1b.common.v1.ResponseMetadataR\bmetadata\x123\n" +
	"\acoupons\x18\x02 \x03(\v2\x19.coupon.v1.EligibleCouponR\acoupons\x12-\n" +
	"\x05error\x18\x03 \x01(\v2\x17.common.v1.ServiceErrorR\x05error\"l\n" +
	"\x15IncrementUsageRequest\x126\n" +
	"\bmetadata\x18\x01 \x01(\v2\x1a.common.v1.RequestMetadataR\bmetadata\x12\x1b\n" +
	"\tcoupon_id\x18\x02 \x01(\tR\bcouponId2\xf4\x02\n" +
	"\rCouponService\x12]\n" +
	"\x10CheckEligibility\x12#.coupon.v1.CouponEligibilityRequest\x1a$.coupon.v1.CouponEligibilityResponse\x12j\n" +
	"\x17ListEligibleAutoCoupons\x12'.coupon.v1.AutoCouponEligibilityRequest\x1a&.coupon.v1.ListEligibleCouponsResponse\x12J\n" +
	"\x0eIncrementUsage\x12 .coupon.v1.IncrementUsageRequest\x1a\x16.google.protobuf.Empty\x12L\n" +
	"\vHealthCheck\x12\x1d.common.v1.HealthCheckRequest\x1a\x1e.common.v1.HealthCheckResponseB8Z6gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/coupon/v1b\x06proto3"

var (
	file_coupon_v1_coupon_service_proto_rawDescOnce sync.Once
	file_coupon_v1_coupon_service_proto_rawDescData []byte
)

func file_coupon_v1_coupon_service_proto_rawDescGZIP() []byte {
	file_coupon_v1_coupon_service_proto_rawDescOnce.Do(func() {
		file_coupon_v1_coupon_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_coupon_v1_coupon_service_proto_rawDesc), len(file_coupon_v1_coupon_service_proto_rawDesc)))
	})
	return file_coupon_v1_coupon_service_proto_rawDescData
}

var file_coupon_v1_coupon_service_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_coupon_v1_coupon_service_proto_goTypes = []any{
	(*CartItem)(nil),                     // 0: coupon.v1.CartItem
	(*CouponEligibilityRequest)(nil),     // 1: coupon.v1.CouponEligibilityRequest
	(*CouponEligibilityResponse)(nil),    // 2: coupon.v1.CouponEligibilityResponse
	(*AutoCouponEligibilityRequest)(nil), // 3: coupon.v1.AutoCouponEligibilityRequest
	(*Coupon)(nil),                       // 4: coupon.v1.Coupon
	(*EligibleCoupon)(nil),               // 5: coupon.v1.EligibleCoupon
	(*ListEligibleCouponsResponse)(nil),  // 6: coupon.v1.ListEligibleCouponsResponse
	(*IncrementUsageRequest)(nil),        // 7: coupon.v1.IncrementUsageRequest
	(*v1.RequestMetadata)(nil),           // 8: common.v1.RequestMetadata
	(*timestamppb.Timestamp)(nil),        // 9: google.protobuf.Timestamp
	(*v1.ResponseMetadata)(nil),          // 10: common.v1.ResponseMetadata
	(*v1.ServiceError)(nil),              // 11: common.v1.ServiceError
	(*v1.HealthCheckRequest)(nil),        // 12: common.v1.HealthCheckRequest
	(*emptypb.Empty)(nil),                // 13: google.protobuf.Empty
	(*v1.HealthCheckResponse)(nil),       // 14: common.v1.HealthCheckResponse
}
var file_coupon_v1_coupon_service_proto_depIdxs = []int32{
	8,  // 0: coupon.v1.CouponEligibilityRequest.metadata:type_name -> common.v1.RequestMetadata
	9,  // 1: coupon.v1.CouponEligibilityRequest.order_timestamp:type_name -> google.protobuf.Timestamp
	0,  // 2: coupon.v1.CouponEligibilityRequest.cart_items:type_name -> coupon.v1.CartItem
	10, // 3: coupon.v1.CouponEligibilityResponse.metadata:type_name -> common.v1.ResponseMetadata
	11, // 4: coupon.v1.CouponEligibilityResponse.error:type_name -> common.v1.ServiceError
	8,  // 5: coupon.v1.AutoCouponEligibilityRequest.metadata:type_name -> common.v1.RequestMetadata
	9,  // 6: coupon.v1.AutoCouponEligibilityRequest.order_timestamp:type_name -> google.protobuf.Timestamp
	0,  // 7: coupon.v1.AutoCouponEligibilityRequest.cart_items:type_name -> coupon.v1.CartItem
	4,  // 8: coupon.v1.EligibleCoupon.coupon:type_name -> coupon.v1.Coupon
	10, // 9: coupon.v1.ListEligibleCouponsResponse.metadata:type_name -> common.v1.ResponseMetadata
	5,  // 10: coupon.v1.ListEligibleCouponsResponse.coupons:type_name -> coupon.v1.EligibleCoupon
	11, // 11: coupon.v1.ListEligibleCouponsResponse.error:type_name -> common.v1.ServiceError
	8,  // 12: coupon.v1.IncrementUsageRequest.metadata:type_name -> common.v1.RequestMetadata
	1,  // 13: coupon.v1.CouponService.CheckEligibility:input_type -> coupon.v1.CouponEligibilityRequest
	3,  // 14: coupon.v1.CouponService.ListEligibleAutoCoupons:input_type -> coupon.v1.AutoCouponEligibilityRequest
	7,  // 15: coupon.v1.CouponService.IncrementUsage:input_type -> coupon.v1.IncrementUsageRequest
	12, // 16: coupon.v1.CouponService.HealthCheck:input_type -> common.v1.HealthCheckRequest
	2,  // 17: coupon.v1.CouponService.CheckEligibility:output_type -> coupon.v1.CouponEligibilityResponse
	6,  // 18: coupon.v1.CouponService.ListEligibleAutoCoupons:output_type -> coupon.v1.ListEligibleCouponsResponse
	13, // 19: coupon.v1.CouponService.IncrementUsage:output_type -> google.protobuf.Empty
	14, // 20: coupon.v1.CouponService.HealthCheck:output_type -> common.v1.HealthCheckResponse
	17, // [17:21] is the sub-list for method output_type
	13, // [13:17] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_coupon_v1_coupon_service_proto_init() }
func file_coupon_v1_coupon_service_proto_init() {
	if File_coupon_v1_coupon_service_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_coupon_v1_coupon_service_proto_rawDesc), len(file_coupon_v1_coupon_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_coupon_v1_coupon_service_proto_goTypes,
		DependencyIndexes: file_coupon_v1_coupon_service_proto_depIdxs,
		MessageInfos:      file_coupon_v1_coupon_service_proto_msgTypes,
	}.Build()
	File_coupon_v1_coupon_service_proto = out.File
	file_coupon_v1_coupon_service_proto_goTypes = nil
	file_coupon_v1_coupon_service_proto_depIdxs = nil
}
