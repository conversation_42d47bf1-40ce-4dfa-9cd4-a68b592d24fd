// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: coupon/v1/coupon_service.proto

package v1

import (
	context "context"
	v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/common/v1"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	CouponService_CheckEligibility_FullMethodName        = "/coupon.v1.CouponService/CheckEligibility"
	CouponService_ListEligibleAutoCoupons_FullMethodName = "/coupon.v1.CouponService/ListEligibleAutoCoupons"
	CouponService_IncrementUsage_FullMethodName          = "/coupon.v1.CouponService/IncrementUsage"
	CouponService_HealthCheck_FullMethodName             = "/coupon.v1.CouponService/HealthCheck"
)

// CouponServiceClient is the client API for CouponService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CouponServiceClient interface {
	// Check whether a coupon can be applied to an order
	CheckEligibility(ctx context.Context, in *CouponEligibilityRequest, opts ...grpc.CallOption) (*CouponEligibilityResponse, error)
	// Suggest automatic coupons the system may apply
	ListEligibleAutoCoupons(ctx context.Context, in *AutoCouponEligibilityRequest, opts ...grpc.CallOption) (*ListEligibleCouponsResponse, error)
	// Update usage count after a successful order
	IncrementUsage(ctx context.Context, in *IncrementUsageRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Health check
	HealthCheck(ctx context.Context, in *v1.HealthCheckRequest, opts ...grpc.CallOption) (*v1.HealthCheckResponse, error)
}

type couponServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewCouponServiceClient(cc grpc.ClientConnInterface) CouponServiceClient {
	return &couponServiceClient{cc}
}

func (c *couponServiceClient) CheckEligibility(ctx context.Context, in *CouponEligibilityRequest, opts ...grpc.CallOption) (*CouponEligibilityResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CouponEligibilityResponse)
	err := c.cc.Invoke(ctx, CouponService_CheckEligibility_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *couponServiceClient) ListEligibleAutoCoupons(ctx context.Context, in *AutoCouponEligibilityRequest, opts ...grpc.CallOption) (*ListEligibleCouponsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListEligibleCouponsResponse)
	err := c.cc.Invoke(ctx, CouponService_ListEligibleAutoCoupons_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *couponServiceClient) IncrementUsage(ctx context.Context, in *IncrementUsageRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, CouponService_IncrementUsage_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *couponServiceClient) HealthCheck(ctx context.Context, in *v1.HealthCheckRequest, opts ...grpc.CallOption) (*v1.HealthCheckResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(v1.HealthCheckResponse)
	err := c.cc.Invoke(ctx, CouponService_HealthCheck_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CouponServiceServer is the server API for CouponService service.
// All implementations should embed UnimplementedCouponServiceServer
// for forward compatibility.
type CouponServiceServer interface {
	// Check whether a coupon can be applied to an order
	CheckEligibility(context.Context, *CouponEligibilityRequest) (*CouponEligibilityResponse, error)
	// Suggest automatic coupons the system may apply
	ListEligibleAutoCoupons(context.Context, *AutoCouponEligibilityRequest) (*ListEligibleCouponsResponse, error)
	// Update usage count after a successful order
	IncrementUsage(context.Context, *IncrementUsageRequest) (*emptypb.Empty, error)
	// Health check
	HealthCheck(context.Context, *v1.HealthCheckRequest) (*v1.HealthCheckResponse, error)
}

// UnimplementedCouponServiceServer should be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedCouponServiceServer struct{}

func (UnimplementedCouponServiceServer) CheckEligibility(context.Context, *CouponEligibilityRequest) (*CouponEligibilityResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckEligibility not implemented")
}
func (UnimplementedCouponServiceServer) ListEligibleAutoCoupons(context.Context, *AutoCouponEligibilityRequest) (*ListEligibleCouponsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListEligibleAutoCoupons not implemented")
}
func (UnimplementedCouponServiceServer) IncrementUsage(context.Context, *IncrementUsageRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IncrementUsage not implemented")
}
func (UnimplementedCouponServiceServer) HealthCheck(context.Context, *v1.HealthCheckRequest) (*v1.HealthCheckResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HealthCheck not implemented")
}
func (UnimplementedCouponServiceServer) testEmbeddedByValue() {}

// UnsafeCouponServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CouponServiceServer will
// result in compilation errors.
type UnsafeCouponServiceServer interface {
	mustEmbedUnimplementedCouponServiceServer()
}

func RegisterCouponServiceServer(s grpc.ServiceRegistrar, srv CouponServiceServer) {
	// If the following call pancis, it indicates UnimplementedCouponServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&CouponService_ServiceDesc, srv)
}

func _CouponService_CheckEligibility_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CouponEligibilityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CouponServiceServer).CheckEligibility(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CouponService_CheckEligibility_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CouponServiceServer).CheckEligibility(ctx, req.(*CouponEligibilityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CouponService_ListEligibleAutoCoupons_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AutoCouponEligibilityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CouponServiceServer).ListEligibleAutoCoupons(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CouponService_ListEligibleAutoCoupons_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CouponServiceServer).ListEligibleAutoCoupons(ctx, req.(*AutoCouponEligibilityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CouponService_IncrementUsage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IncrementUsageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CouponServiceServer).IncrementUsage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CouponService_IncrementUsage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CouponServiceServer).IncrementUsage(ctx, req.(*IncrementUsageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CouponService_HealthCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(v1.HealthCheckRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CouponServiceServer).HealthCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CouponService_HealthCheck_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CouponServiceServer).HealthCheck(ctx, req.(*v1.HealthCheckRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// CouponService_ServiceDesc is the grpc.ServiceDesc for CouponService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CouponService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "coupon.v1.CouponService",
	HandlerType: (*CouponServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CheckEligibility",
			Handler:    _CouponService_CheckEligibility_Handler,
		},
		{
			MethodName: "ListEligibleAutoCoupons",
			Handler:    _CouponService_ListEligibleAutoCoupons_Handler,
		},
		{
			MethodName: "IncrementUsage",
			Handler:    _CouponService_IncrementUsage_Handler,
		},
		{
			MethodName: "HealthCheck",
			Handler:    _CouponService_HealthCheck_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "coupon/v1/coupon_service.proto",
}
