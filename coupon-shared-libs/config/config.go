package config

import (
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/spf13/viper"
)

type DownstreamServicesConfig struct {
	AuthServiceAddr    string `mapstructure:"auth_service_addr"`
	UserServiceAddr    string `mapstructure:"user_service_addr"`
	ProductServiceAddr string `mapstructure:"product_service_addr"`
	CouponServiceAddr  string `mapstructure:"coupon_service_addr"`
	OrderServiceAddr   string `mapstructure:"order_service_addr"`
}

type ServiceRegistryConfig struct {
	Enabled           bool              `mapstructure:"enabled"`
	DatabaseHost      string            `mapstructure:"database_host"`
	DatabasePort      int               `mapstructure:"database_port"`
	DatabaseName      string            `mapstructure:"database_name"`
	DatabaseUser      string            `mapstructure:"database_user"`
	DatabasePassword  string            `mapstructure:"database_password"`
	HeartbeatInterval time.Duration     `mapstructure:"heartbeat_interval"`
	HealthCheckTTL    time.Duration     `mapstructure:"health_check_ttl"`
	CleanupInterval   time.Duration     `mapstructure:"cleanup_interval"`
	Tags              []string          `mapstructure:"tags"`
	Metadata          map[string]string `mapstructure:"metadata"`
}

type ServiceDiscoveryConfig struct {
	Enabled         bool          `mapstructure:"enabled"`
	RefreshInterval time.Duration `mapstructure:"refresh_interval"`
	HealthCheckTTL  time.Duration `mapstructure:"health_check_ttl"`
	LoadBalancing   string        `mapstructure:"load_balancing"`
	MaxRetries      int           `mapstructure:"max_retries"`
	RetryDelay      time.Duration `mapstructure:"retry_delay"`
	CacheEnabled    bool          `mapstructure:"cache_enabled"`
	CacheTTL        time.Duration `mapstructure:"cache_ttl"`
}

type Config struct {
	Service            ServiceConfig            `mapstructure:"service"`
	Database           DatabaseConfig           `mapstructure:"database"`
	Redis              RedisConfig              `mapstructure:"redis"`
	GRPC               GRPCConfig               `mapstructure:"grpc"`
	Kafka              KafkaConfig              `mapstructure:"kafka"`
	Jaeger             JaegerConfig             `mapstructure:"jaeger"`
	Auth               AuthConfig               `mapstructure:"auth"`
	Logging            LoggingConfig            `mapstructure:"logging"`
	Metrics            MetricsConfig            `mapstructure:"metrics"`
	DownstreamServices DownstreamServicesConfig `mapstructure:"downstream_services"`
	ServiceRegistry    ServiceRegistryConfig    `mapstructure:"service_registry"`
	ServiceDiscovery   ServiceDiscoveryConfig   `mapstructure:"service_discovery"`
}

type ServiceConfig struct {
	Name        string `mapstructure:"name"`
	Version     string `mapstructure:"version"`
	Environment string `mapstructure:"environment"`
	Port        int    `mapstructure:"port"`
	GRPCPort    int    `mapstructure:"grpc_port"`
	ClientID    string `mapstructure:"client_id"`
	ClientKey   string `mapstructure:"client_key"`
}

type DatabaseConfig struct {
	Host         string        `mapstructure:"host"`
	Port         int           `mapstructure:"port"`
	User         string        `mapstructure:"user"`
	Password     string        `mapstructure:"password"`
	Name         string        `mapstructure:"name"`
	SSLMode      string        `mapstructure:"ssl_mode"`
	MaxOpenConns int           `mapstructure:"max_open_conns"`
	MaxIdleConns int           `mapstructure:"max_idle_conns"`
	MaxLifetime  time.Duration `mapstructure:"max_lifetime"`
}

type RedisConfig struct {
	Host     string `mapstructure:"host"`
	Port     int    `mapstructure:"port"`
	Password string `mapstructure:"password"`
	DB       int    `mapstructure:"db"`
}

type GRPCConfig struct {
	Host              string        `mapstructure:"host"`
	Port              int           `mapstructure:"port"`
	MaxReceiveSize    int           `mapstructure:"max_receive_size"`
	MaxSendSize       int           `mapstructure:"max_send_size"`
	ConnectionTimeout time.Duration `mapstructure:"connection_timeout"`
	KeepaliveTime     time.Duration `mapstructure:"keepalive_time"`
	KeepaliveTimeout  time.Duration `mapstructure:"keepalive_timeout"`
	MaxConnectionIdle time.Duration `mapstructure:"max_connection_idle"`
}

type KafkaConfig struct {
	Brokers []string          `mapstructure:"brokers"`
	GroupID string            `mapstructure:"group_id"`
	Topics  KafkaTopicsConfig `mapstructure:"topics"`
}

type KafkaTopicsConfig struct {
	UserRegistered string `mapstructure:"user_registered"`
	OrderCreated   string `mapstructure:"order_created"`
}

type JaegerConfig struct {
	Host string `mapstructure:"host"`
	Port int    `mapstructure:"port"`
}

type AuthConfig struct {
	JWTSecret              string        `mapstructure:"jwt_secret"`
	JWTExpiration          time.Duration `mapstructure:"jwt_expiration"`
	RefreshTokenExpiration time.Duration `mapstructure:"refresh_token_expiration"`
}

type LoggingConfig struct {
	Level  string `mapstructure:"level"`
	Format string `mapstructure:"format"`
}

type MetricsConfig struct {
	Port int    `mapstructure:"port"`
	Path string `mapstructure:"path"`
}

func Load() (*Config, error) {
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath(".")
	viper.AddConfigPath("./config")
	viper.AddConfigPath("/etc/coupon-services/config")

	viper.AutomaticEnv()
	viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))

	// Enable environment variable expansion in config values
	viper.SetEnvPrefix("")

	setDefaults()

	if err := viper.ReadInConfig(); err != nil {
		return nil, fmt.Errorf("failed to read config file: %w", err)
	}

	// Manually expand environment variables that Viper doesn't handle
	expandEnvVars()

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	return &config, nil
}

// expandEnvVars manually expands environment variables in config values
func expandEnvVars() {
	// Database configuration
	if user := viper.GetString("database.user"); strings.Contains(user, "${") {
		viper.Set("database.user", expandEnvVar(user))
	}
	if password := viper.GetString("database.password"); strings.Contains(password, "${") {
		viper.Set("database.password", expandEnvVar(password))
	}
	if name := viper.GetString("database.name"); strings.Contains(name, "${") {
		viper.Set("database.name", expandEnvVar(name))
	}

	// Redis configuration
	if password := viper.GetString("redis.password"); strings.Contains(password, "${") {
		viper.Set("redis.password", expandEnvVar(password))
	}

	// Auth configuration
	if secret := viper.GetString("auth.jwt_secret"); strings.Contains(secret, "${") {
		viper.Set("auth.jwt_secret", expandEnvVar(secret))
	}

	// Service configuration
	if key := viper.GetString("service.client_key"); strings.Contains(key, "${") {
		viper.Set("service.client_key", expandEnvVar(key))
	}
}

// expandEnvVar expands a single environment variable
func expandEnvVar(value string) string {
	// Simple expansion for ${VAR} format
	if strings.HasPrefix(value, "${") && strings.HasSuffix(value, "}") {
		envVar := value[2 : len(value)-1]
		if envValue := os.Getenv(envVar); envValue != "" {
			return envValue
		}
	}
	return value
}

func setDefaults() {
	viper.SetDefault("service.environment", "development")
	viper.SetDefault("service.port", 8080)
	viper.SetDefault("service.grpc_port", 9090)
	viper.SetDefault("database.host", "localhost")
	viper.SetDefault("database.port", 5432)
	viper.SetDefault("database.ssl_mode", "disable")
	viper.SetDefault("database.max_open_conns", 25)
	viper.SetDefault("database.max_idle_conns", 25)
	viper.SetDefault("database.max_lifetime", time.Hour)
	viper.SetDefault("redis.host", "localhost")
	viper.SetDefault("redis.port", 6379)
	viper.SetDefault("redis.db", 0)
	viper.SetDefault("grpc.host", "localhost")
	viper.SetDefault("grpc.max_receive_size", 4*1024*1024)
	viper.SetDefault("grpc.max_send_size", 4*1024*1024)
	viper.SetDefault("grpc.connection_timeout", 5*time.Second)
	viper.SetDefault("grpc.keepalive_time", 30*time.Second)
	viper.SetDefault("grpc.keepalive_timeout", 5*time.Second)
	viper.SetDefault("grpc.max_connection_idle", 90*time.Second)
	viper.SetDefault("kafka.brokers", []string{"localhost:9092"})
	viper.SetDefault("jaeger.host", "localhost")
	viper.SetDefault("jaeger.port", 14268)
	viper.SetDefault("auth.jwt_expiration", 1*time.Hour)
	viper.SetDefault("auth.refresh_token_expiration", 720*time.Hour)
	viper.SetDefault("logging.level", "info")
	viper.SetDefault("logging.format", "json")
	viper.SetDefault("metrics.port", 2112)
	viper.SetDefault("metrics.path", "/metrics")

	viper.SetDefault("downstream_services.auth_service_addr", "auth-service:50051")
	viper.SetDefault("downstream_services.user_service_addr", "user-service:50051")

	// Service Registry defaults
	viper.SetDefault("service_registry.enabled", false)
	viper.SetDefault("service_registry.database_host", "localhost")
	viper.SetDefault("service_registry.database_port", 5432)
	viper.SetDefault("service_registry.database_name", "service_registry")
	viper.SetDefault("service_registry.database_user", "registry")
	viper.SetDefault("service_registry.database_password", "registry")
	viper.SetDefault("service_registry.heartbeat_interval", 30*time.Second)
	viper.SetDefault("service_registry.health_check_ttl", 90*time.Second)
	viper.SetDefault("service_registry.cleanup_interval", 60*time.Second)
	viper.SetDefault("service_registry.tags", []string{})
	viper.SetDefault("service_registry.metadata", map[string]string{})

	// Service Discovery defaults
	viper.SetDefault("service_discovery.enabled", false)
	viper.SetDefault("service_discovery.refresh_interval", 30*time.Second)
	viper.SetDefault("service_discovery.health_check_ttl", 90*time.Second)
	viper.SetDefault("service_discovery.load_balancing", "round_robin")
	viper.SetDefault("service_discovery.max_retries", 3)
	viper.SetDefault("service_discovery.retry_delay", 1*time.Second)
	viper.SetDefault("service_discovery.cache_enabled", true)
	viper.SetDefault("service_discovery.cache_ttl", 60*time.Second)
}
