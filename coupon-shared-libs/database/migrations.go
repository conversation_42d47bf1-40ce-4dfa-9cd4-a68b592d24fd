package database

import (
	"fmt"

	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
)

// AutoMigrator handles GORM automatic database migrations
type AutoMigrator struct {
	db     *DB
	logger *logging.Logger
}

// NewAutoMigrator creates a new auto migrator
func NewAutoMigrator(db *DB, logger *logging.Logger) *AutoMigrator {
	return &AutoMigrator{
		db:     db,
		logger: logger,
	}
}

// AutoMigrate runs GORM AutoMigrate on the provided models
func (am *AutoMigrator) AutoMigrate(models ...interface{}) error {
	am.logger.Info("Starting GORM AutoMigrate for database schema")

	if len(models) == 0 {
		am.logger.Info("No models provided for migration")
		return nil
	}

	// Run GORM AutoMigrate
	if err := am.db.AutoMigrate(models...); err != nil {
		return fmt.Errorf("failed to auto-migrate database schema: %w", err)
	}

	am.logger.Infof("Successfully migrated %d models", len(models))
	return nil
}

// AutoMigrateWithSeeds runs AutoMigrate and then executes seed functions
func (am *AutoMigrator) AutoMigrateWithSeeds(models []interface{}, seedFuncs ...func(*DB) error) error {
	// First run the migrations
	if err := am.AutoMigrate(models...); err != nil {
		return err
	}

	// Then run seed functions
	for i, seedFunc := range seedFuncs {
		am.logger.Infof("Running seed function %d", i+1)
		if err := seedFunc(am.db); err != nil {
			return fmt.Errorf("failed to run seed function %d: %w", i+1, err)
		}
	}

	am.logger.Info("Database migration and seeding completed successfully")
	return nil
}
