package discovery

import (
	"context"
	"fmt"
	"math/rand"
	"sync"
	"time"

	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/registry"
)

// LoadBalancingStrategy defines the load balancing strategy
type LoadBalancingStrategy string

const (
	RoundRobin LoadBalancingStrategy = "round_robin"
	Random     LoadBalancingStrategy = "random"
	LeastConn  LoadBalancingStrategy = "least_conn"
)

// DiscoveryConfig holds configuration for service discovery
type DiscoveryConfig struct {
	RefreshInterval time.Duration         `mapstructure:"refresh_interval"`
	HealthCheckTTL  time.Duration         `mapstructure:"health_check_ttl"`
	LoadBalancing   LoadBalancingStrategy `mapstructure:"load_balancing"`
	MaxRetries      int                   `mapstructure:"max_retries"`
	RetryDelay      time.Duration         `mapstructure:"retry_delay"`
	CacheEnabled    bool                  `mapstructure:"cache_enabled"`
	CacheTTL        time.Duration         `mapstructure:"cache_ttl"`
}

// ServiceInstance represents a discovered service instance
type ServiceInstance struct {
	*registry.ServiceInstance
	ConnectionCount int       `json:"connection_count"`
	LastUsed        time.Time `json:"last_used"`
}

// ServiceDiscoveryClient handles service discovery and load balancing
type ServiceDiscoveryClient struct {
	config     *DiscoveryConfig
	repository registry.Repository
	logger     *logging.Logger

	// Service cache
	serviceCache map[string][]*ServiceInstance
	cacheMutex   sync.RWMutex
	lastRefresh  map[string]time.Time

	// Load balancing state
	roundRobinIndex map[string]int
	indexMutex      sync.RWMutex

	// Lifecycle
	ctx    context.Context
	cancel context.CancelFunc
	wg     sync.WaitGroup
}

// NewServiceDiscoveryClient creates a new service discovery client
func NewServiceDiscoveryClient(cfg *DiscoveryConfig, repo registry.Repository, logger *logging.Logger) *ServiceDiscoveryClient {
	if cfg == nil {
		cfg = &DiscoveryConfig{
			RefreshInterval: 30 * time.Second,
			HealthCheckTTL:  90 * time.Second,
			LoadBalancing:   RoundRobin,
			MaxRetries:      3,
			RetryDelay:      1 * time.Second,
			CacheEnabled:    true,
			CacheTTL:        60 * time.Second,
		}
	}

	ctx, cancel := context.WithCancel(context.Background())

	client := &ServiceDiscoveryClient{
		config:          cfg,
		repository:      repo,
		logger:          logger,
		serviceCache:    make(map[string][]*ServiceInstance),
		lastRefresh:     make(map[string]time.Time),
		roundRobinIndex: make(map[string]int),
		ctx:             ctx,
		cancel:          cancel,
	}

	// Start background refresh if caching is enabled
	if cfg.CacheEnabled {
		client.wg.Add(1)
		go client.refreshLoop()
	}

	return client
}

// DiscoverService discovers healthy instances of a service
func (c *ServiceDiscoveryClient) DiscoverService(ctx context.Context, serviceName string) ([]*ServiceInstance, error) {
	// Check cache first if enabled
	if c.config.CacheEnabled {
		if instances := c.getCachedInstances(serviceName); instances != nil {
			return instances, nil
		}
	}

	// Fetch from repository
	instances, err := c.fetchServiceInstances(ctx, serviceName)
	if err != nil {
		return nil, err
	}

	// Update cache if enabled
	if c.config.CacheEnabled {
		c.updateCache(serviceName, instances)
	}

	return instances, nil
}

// GetServiceInstance returns a single service instance using load balancing
func (c *ServiceDiscoveryClient) GetServiceInstance(ctx context.Context, serviceName string) (*ServiceInstance, error) {
	instances, err := c.DiscoverService(ctx, serviceName)
	if err != nil {
		return nil, err
	}

	if len(instances) == 0 {
		return nil, fmt.Errorf("no healthy instances found for service: %s", serviceName)
	}

	// Apply load balancing strategy
	instance := c.selectInstance(serviceName, instances)
	if instance == nil {
		return nil, fmt.Errorf("failed to select instance for service: %s", serviceName)
	}

	// Update usage statistics
	instance.ConnectionCount++
	instance.LastUsed = time.Now()

	c.logger.WithContext(ctx).WithFields(logging.Fields{
		"service_name": serviceName,
		"instance_id":  instance.ID,
		"address":      instance.GetGRPCAddress(),
		"strategy":     c.config.LoadBalancing,
	}).Debug("Selected service instance")

	return instance, nil
}

// GetServiceAddress returns a service address using load balancing
func (c *ServiceDiscoveryClient) GetServiceAddress(ctx context.Context, serviceName string) (string, error) {
	instance, err := c.GetServiceInstance(ctx, serviceName)
	if err != nil {
		return "", err
	}

	return instance.GetGRPCAddress(), nil
}

// RefreshService forces a refresh of service instances for a specific service
func (c *ServiceDiscoveryClient) RefreshService(ctx context.Context, serviceName string) error {
	instances, err := c.fetchServiceInstances(ctx, serviceName)
	if err != nil {
		return err
	}

	c.updateCache(serviceName, instances)

	c.logger.WithContext(ctx).WithFields(logging.Fields{
		"service_name":   serviceName,
		"instance_count": len(instances),
	}).Info("Service instances refreshed")

	return nil
}

// Close stops the discovery client and cleans up resources
func (c *ServiceDiscoveryClient) Close() error {
	c.cancel()
	c.wg.Wait()

	c.cacheMutex.Lock()
	c.serviceCache = make(map[string][]*ServiceInstance)
	c.lastRefresh = make(map[string]time.Time)
	c.cacheMutex.Unlock()

	c.logger.Info("Service discovery client closed")
	return nil
}

// fetchServiceInstances fetches healthy service instances from repository
func (c *ServiceDiscoveryClient) fetchServiceInstances(ctx context.Context, serviceName string) ([]*ServiceInstance, error) {
	registryInstances, err := c.repository.GetHealthyServices(ctx, serviceName)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch service instances: %w", err)
	}

	// Convert to discovery instances
	instances := make([]*ServiceInstance, len(registryInstances))
	for i, regInstance := range registryInstances {
		instances[i] = &ServiceInstance{
			ServiceInstance: regInstance,
			ConnectionCount: 0,
			LastUsed:        time.Time{},
		}
	}

	// Filter out expired instances
	now := time.Now()
	healthyInstances := make([]*ServiceInstance, 0, len(instances))
	for _, instance := range instances {
		if !instance.IsExpired(c.config.HealthCheckTTL) {
			healthyInstances = append(healthyInstances, instance)
		}
	}

	return healthyInstances, nil
}

// getCachedInstances returns cached instances if they're still valid
func (c *ServiceDiscoveryClient) getCachedInstances(serviceName string) []*ServiceInstance {
	c.cacheMutex.RLock()
	defer c.cacheMutex.RUnlock()

	instances, exists := c.serviceCache[serviceName]
	if !exists {
		return nil
	}

	lastRefresh, exists := c.lastRefresh[serviceName]
	if !exists || time.Since(lastRefresh) > c.config.CacheTTL {
		return nil
	}

	return instances
}

// updateCache updates the service cache
func (c *ServiceDiscoveryClient) updateCache(serviceName string, instances []*ServiceInstance) {
	c.cacheMutex.Lock()
	defer c.cacheMutex.Unlock()

	c.serviceCache[serviceName] = instances
	c.lastRefresh[serviceName] = time.Now()
}

// selectInstance selects an instance based on the load balancing strategy
func (c *ServiceDiscoveryClient) selectInstance(serviceName string, instances []*ServiceInstance) *ServiceInstance {
	if len(instances) == 0 {
		return nil
	}

	switch c.config.LoadBalancing {
	case RoundRobin:
		return c.selectRoundRobin(serviceName, instances)
	case Random:
		return c.selectRandom(instances)
	case LeastConn:
		return c.selectLeastConnections(instances)
	default:
		return c.selectRoundRobin(serviceName, instances)
	}
}

// selectRoundRobin selects instance using round-robin strategy
func (c *ServiceDiscoveryClient) selectRoundRobin(serviceName string, instances []*ServiceInstance) *ServiceInstance {
	c.indexMutex.Lock()
	defer c.indexMutex.Unlock()

	index := c.roundRobinIndex[serviceName]
	instance := instances[index%len(instances)]
	c.roundRobinIndex[serviceName] = (index + 1) % len(instances)

	return instance
}

// selectRandom selects instance using random strategy
func (c *ServiceDiscoveryClient) selectRandom(instances []*ServiceInstance) *ServiceInstance {
	index := rand.Intn(len(instances))
	return instances[index]
}

// selectLeastConnections selects instance with least connections
func (c *ServiceDiscoveryClient) selectLeastConnections(instances []*ServiceInstance) *ServiceInstance {
	var selected *ServiceInstance
	minConnections := int(^uint(0) >> 1) // Max int

	for _, instance := range instances {
		if instance.ConnectionCount < minConnections {
			minConnections = instance.ConnectionCount
			selected = instance
		}
	}

	return selected
}

// refreshLoop periodically refreshes cached service instances
func (c *ServiceDiscoveryClient) refreshLoop() {
	defer c.wg.Done()

	ticker := time.NewTicker(c.config.RefreshInterval)
	defer ticker.Stop()

	for {
		select {
		case <-c.ctx.Done():
			return
		case <-ticker.C:
			c.refreshAllCachedServices()
		}
	}
}

// refreshAllCachedServices refreshes all cached services
func (c *ServiceDiscoveryClient) refreshAllCachedServices() {
	c.cacheMutex.RLock()
	serviceNames := make([]string, 0, len(c.serviceCache))
	for serviceName := range c.serviceCache {
		serviceNames = append(serviceNames, serviceName)
	}
	c.cacheMutex.RUnlock()

	for _, serviceName := range serviceNames {
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		if err := c.RefreshService(ctx, serviceName); err != nil {
			c.logger.WithError(err).WithFields(logging.Fields{
				"service_name": serviceName,
			}).Warn("Failed to refresh service instances")
		}
		cancel()
	}
}
