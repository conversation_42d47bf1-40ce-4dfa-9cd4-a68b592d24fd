package registry

import (
	"context"
	"fmt"
	"time"

	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/keepalive"
	"google.golang.org/grpc/metadata"

	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/metrics"
)

// ServiceClient wraps gRPC client with automatic service discovery and connection management
type ServiceClient struct {
	serviceName string
	manager     *Manager
	config      *config.GRPCConfig
	logger      *logging.Logger
	metrics     *metrics.Metrics
	clientID    string
	clientKey   string

	// Connection management
	conn        *grpc.ClientConn
	currentAddr string
	lastRefresh time.Time
	refreshTTL  time.Duration
}

// NewServiceClient creates a new service client with automatic discovery and connection management
func NewServiceClient(serviceName string, manager *Manager, cfg *config.GRPCConfig, logger *logging.Logger, metrics *metrics.Metrics, clientID, clientKey string) (*ServiceClient, error) {
	client := &ServiceClient{
		serviceName: serviceName,
		manager:     manager,
		config:      cfg,
		logger:      logger,
		metrics:     metrics,
		clientID:    clientID,
		clientKey:   clientKey,
		refreshTTL:  30 * time.Second, // Refresh connection every 30 seconds
	}

	// Establish initial connection
	if err := client.connect(); err != nil {
		return nil, fmt.Errorf("failed to establish initial connection: %w", err)
	}

	return client, nil
}

// GetConnection returns the gRPC connection, refreshing if needed
func (c *ServiceClient) GetConnection(ctx context.Context) (*grpc.ClientConn, error) {
	// Check if we need to refresh the connection
	if time.Since(c.lastRefresh) > c.refreshTTL {
		if err := c.refreshConnection(ctx); err != nil {
			c.logger.WithError(err).Warn("Failed to refresh connection, using existing")
		}
	}

	if c.conn == nil {
		return nil, fmt.Errorf("no connection available for service: %s", c.serviceName)
	}

	return c.conn, nil
}

// Close closes the gRPC connection
func (c *ServiceClient) Close() error {
	if c.conn != nil {
		return c.conn.Close()
	}
	return nil
}

// connect establishes a connection to the service
func (c *ServiceClient) connect() error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// Try service discovery first if available
	var addr string
	var err error

	if c.manager != nil && c.manager.IsDiscoveryEnabled() {
		addr, err = c.manager.GetServiceAddress(ctx, c.serviceName)
		if err != nil {
			c.logger.WithFields(logging.Fields{
				"service_name": c.serviceName,
				"error":        err.Error(),
			}).Warn("Service discovery failed, falling back to config")
		}
	}

	// Fallback to configured address if discovery fails
	if addr == "" {
		addr = c.getConfiguredAddress()
	}

	if addr == "" {
		return fmt.Errorf("no address available for service: %s", c.serviceName)
	}

	// Create gRPC connection
	conn, err := c.createConnection(addr)
	if err != nil {
		return fmt.Errorf("failed to connect to %s: %w", addr, err)
	}

	// Close existing connection if any
	if c.conn != nil {
		c.conn.Close()
	}

	c.conn = conn
	c.currentAddr = addr
	c.lastRefresh = time.Now()

	c.logger.WithFields(logging.Fields{
		"service_name": c.serviceName,
		"address":      addr,
	}).Info("Connected to service")

	return nil
}

// refreshConnection refreshes the connection if a better address is available
func (c *ServiceClient) refreshConnection(ctx context.Context) error {
	if c.manager == nil || !c.manager.IsDiscoveryEnabled() {
		return nil // No discovery available
	}

	// Get current best address
	addr, err := c.manager.GetServiceAddress(ctx, c.serviceName)
	if err != nil {
		return fmt.Errorf("failed to get service address: %w", err)
	}

	// If address hasn't changed, just update refresh time
	if addr == c.currentAddr {
		c.lastRefresh = time.Now()
		return nil
	}

	// Create new connection
	conn, err := c.createConnection(addr)
	if err != nil {
		return fmt.Errorf("failed to connect to new address %s: %w", addr, err)
	}

	// Replace connection
	oldConn := c.conn
	c.conn = conn
	c.currentAddr = addr
	c.lastRefresh = time.Now()

	// Close old connection
	if oldConn != nil {
		go oldConn.Close() // Close asynchronously to avoid blocking
	}

	c.logger.WithFields(logging.Fields{
		"service_name": c.serviceName,
		"old_address":  c.currentAddr,
		"new_address":  addr,
	}).Info("Refreshed service connection")

	return nil
}

// createConnection creates a gRPC connection with proper configuration
func (c *ServiceClient) createConnection(addr string) (*grpc.ClientConn, error) {
	opts := []grpc.DialOption{
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithKeepaliveParams(keepalive.ClientParameters{
			Time:                c.config.KeepaliveTime,
			Timeout:             c.config.KeepaliveTimeout,
			PermitWithoutStream: true,
		}),
		grpc.WithDefaultCallOptions(
			grpc.MaxCallRecvMsgSize(c.config.MaxReceiveSize),
			grpc.MaxCallSendMsgSize(c.config.MaxSendSize),
		),
		grpc.WithUnaryInterceptor(c.createClientInterceptor()),
	}

	return grpc.NewClient(addr, opts...)
}

// createClientInterceptor creates a client interceptor that adds authentication headers
func (c *ServiceClient) createClientInterceptor() grpc.UnaryClientInterceptor {
	return func(ctx context.Context, method string, req, reply interface{}, cc *grpc.ClientConn, invoker grpc.UnaryInvoker, opts ...grpc.CallOption) error {
		// Add authentication headers
		if c.clientID != "" && c.clientKey != "" {
			ctx = metadata.AppendToOutgoingContext(ctx,
				"client-id", c.clientID,
				"client-key", c.clientKey,
			)
		}

		start := time.Now()

		// Log request
		c.logger.WithFields(logging.Fields{
			"method":       method,
			"service_name": c.serviceName,
			"address":      c.currentAddr,
		}).Debug("gRPC client request started")

		// Make the call
		err := invoker(ctx, method, req, reply, cc, opts...)

		duration := time.Since(start)

		// Record metrics
		if c.metrics != nil {
			status := "success"
			if err != nil {
				status = "error"
			}
			c.metrics.RecordGRPCRequest("grpc-client", method, status, duration)
		}

		// Log response
		fields := logging.Fields{
			"method":       method,
			"service_name": c.serviceName,
			"address":      c.currentAddr,
			"duration":     duration.String(),
		}

		if err != nil {
			fields["error"] = err.Error()
			c.logger.WithFields(fields).Error("gRPC client request failed")
		} else {
			c.logger.WithFields(fields).Debug("gRPC client request completed")
		}

		return err
	}
}

// getConfiguredAddress returns the configured address for the service
func (c *ServiceClient) getConfiguredAddress() string {
	// This would typically come from configuration
	// For now, return empty string to force discovery
	return ""
}

// Invoke makes a gRPC call with automatic connection management
func (c *ServiceClient) Invoke(ctx context.Context, method string, args interface{}, reply interface{}, opts ...grpc.CallOption) error {
	conn, err := c.GetConnection(ctx)
	if err != nil {
		return err
	}

	return conn.Invoke(ctx, method, args, reply, opts...)
}

// NewStream creates a new gRPC stream with automatic connection management
func (c *ServiceClient) NewStream(ctx context.Context, desc *grpc.StreamDesc, method string, opts ...grpc.CallOption) (grpc.ClientStream, error) {
	conn, err := c.GetConnection(ctx)
	if err != nil {
		return nil, err
	}

	return conn.NewStream(ctx, desc, method, opts...)
}
