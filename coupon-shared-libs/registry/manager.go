package registry

import (
	"context"
	"fmt"
	"sync"
	"time"

	"gorm.io/gorm"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/database"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/discovery"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
)

// Manager manages service registration and discovery
type Manager struct {
	config     *config.Config
	logger     *logging.Logger
	
	// Database connection for registry
	registryDB *database.DB
	repository Repository
	
	// Service registration
	registrar     *ServiceRegistrar
	healthReporter *HealthReporter
	
	// Service discovery
	discoveryClient *discovery.ServiceDiscoveryClient
	
	// Lifecycle
	ctx    context.Context
	cancel context.CancelFunc
	wg     sync.WaitGroup
	
	// State
	started bool
	mu      sync.RWMutex
}

// NewManager creates a new service registry manager
func NewManager(cfg *config.Config, logger *logging.Logger) (*Manager, error) {
	ctx, cancel := context.WithCancel(context.Background())
	
	manager := &Manager{
		config: cfg,
		logger: logger,
		ctx:    ctx,
		cancel: cancel,
	}
	
	// Initialize registry database if service registry is enabled
	if cfg.ServiceRegistry.Enabled {
		if err := manager.initializeRegistryDatabase(); err != nil {
			cancel()
			return nil, fmt.Errorf("failed to initialize registry database: %w", err)
		}
		
		// Create repository
		manager.repository = NewRepository(manager.registryDB.DB, logger)
		
		// Create service registrar
		manager.registrar = NewServiceRegistrarFromConfig(cfg, manager.repository, logger)
		
		// Create health reporter
		manager.healthReporter = NewHealthReporter(manager.registrar, manager.repository, logger)
	}
	
	// Initialize service discovery if enabled
	if cfg.ServiceDiscovery.Enabled {
		if manager.repository == nil {
			// If discovery is enabled but registry is not, we need to connect to registry DB
			if err := manager.initializeRegistryDatabase(); err != nil {
				cancel()
				return nil, fmt.Errorf("failed to initialize registry database for discovery: %w", err)
			}
			manager.repository = NewRepository(manager.registryDB.DB, logger)
		}
		
		discoveryConfig := &discovery.DiscoveryConfig{
			RefreshInterval: cfg.ServiceDiscovery.RefreshInterval,
			HealthCheckTTL:  cfg.ServiceDiscovery.HealthCheckTTL,
			LoadBalancing:   discovery.LoadBalancingStrategy(cfg.ServiceDiscovery.LoadBalancing),
			MaxRetries:      cfg.ServiceDiscovery.MaxRetries,
			RetryDelay:      cfg.ServiceDiscovery.RetryDelay,
			CacheEnabled:    cfg.ServiceDiscovery.CacheEnabled,
			CacheTTL:        cfg.ServiceDiscovery.CacheTTL,
		}
		
		manager.discoveryClient = discovery.NewServiceDiscoveryClient(discoveryConfig, manager.repository, logger)
	}
	
	return manager, nil
}

// Start starts the service registry manager
func (m *Manager) Start(ctx context.Context) error {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	if m.started {
		return fmt.Errorf("manager already started")
	}
	
	// Start service registration if enabled
	if m.config.ServiceRegistry.Enabled && m.registrar != nil {
		if err := m.registrar.Register(ctx); err != nil {
			return fmt.Errorf("failed to register service: %w", err)
		}
		
		// Start health reporter
		if m.healthReporter != nil {
			m.healthReporter.Start()
		}
		
		// Start cleanup routine
		m.wg.Add(1)
		go m.cleanupLoop()
	}
	
	m.started = true
	
	m.logger.WithFields(logging.Fields{
		"registry_enabled":  m.config.ServiceRegistry.Enabled,
		"discovery_enabled": m.config.ServiceDiscovery.Enabled,
	}).Info("Service registry manager started")
	
	return nil
}

// Stop stops the service registry manager
func (m *Manager) Stop(ctx context.Context) error {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	if !m.started {
		return nil
	}
	
	// Stop health reporter
	if m.healthReporter != nil {
		m.healthReporter.Stop()
	}
	
	// Deregister service
	if m.registrar != nil {
		if err := m.registrar.Deregister(ctx); err != nil {
			m.logger.WithError(err).Error("Failed to deregister service")
		}
	}
	
	// Stop discovery client
	if m.discoveryClient != nil {
		if err := m.discoveryClient.Close(); err != nil {
			m.logger.WithError(err).Error("Failed to close discovery client")
		}
	}
	
	// Stop background routines
	m.cancel()
	m.wg.Wait()
	
	// Close registry database
	if m.registryDB != nil {
		if err := m.registryDB.Close(); err != nil {
			m.logger.WithError(err).Error("Failed to close registry database")
		}
	}
	
	m.started = false
	
	m.logger.Info("Service registry manager stopped")
	return nil
}

// GetRegistrar returns the service registrar
func (m *Manager) GetRegistrar() *ServiceRegistrar {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.registrar
}

// GetHealthReporter returns the health reporter
func (m *Manager) GetHealthReporter() *HealthReporter {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.healthReporter
}

// GetDiscoveryClient returns the service discovery client
func (m *Manager) GetDiscoveryClient() *discovery.ServiceDiscoveryClient {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.discoveryClient
}

// GetRepository returns the registry repository
func (m *Manager) GetRepository() Repository {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.repository
}

// AddHealthCheck adds a health check to the health reporter
func (m *Manager) AddHealthCheck(name string, checkFunc HealthCheckFunc) error {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	if m.healthReporter == nil {
		return fmt.Errorf("health reporter not available")
	}
	
	m.healthReporter.AddHealthCheck(name, checkFunc)
	return nil
}

// AddServiceEndpoint adds an endpoint to the service registrar
func (m *Manager) AddServiceEndpoint(name, path, method, protocol string, isPublic bool, description string) error {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	if m.registrar == nil {
		return fmt.Errorf("service registrar not available")
	}
	
	m.registrar.AddEndpoint(name, path, method, protocol, isPublic, description)
	return nil
}

// DiscoverService discovers service instances
func (m *Manager) DiscoverService(ctx context.Context, serviceName string) ([]*discovery.ServiceInstance, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	if m.discoveryClient == nil {
		return nil, fmt.Errorf("service discovery not enabled")
	}
	
	return m.discoveryClient.DiscoverService(ctx, serviceName)
}

// GetServiceAddress gets a service address using load balancing
func (m *Manager) GetServiceAddress(ctx context.Context, serviceName string) (string, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	if m.discoveryClient == nil {
		return "", fmt.Errorf("service discovery not enabled")
	}
	
	return m.discoveryClient.GetServiceAddress(ctx, serviceName)
}

// initializeRegistryDatabase initializes the registry database connection
func (m *Manager) initializeRegistryDatabase() error {
	// Create database config for registry
	dbConfig := &config.DatabaseConfig{
		Host:         m.config.ServiceRegistry.DatabaseHost,
		Port:         m.config.ServiceRegistry.DatabasePort,
		User:         m.config.ServiceRegistry.DatabaseUser,
		Password:     m.config.ServiceRegistry.DatabasePassword,
		Name:         m.config.ServiceRegistry.DatabaseName,
		SSLMode:      "disable",
		MaxOpenConns: 10,
		MaxIdleConns: 5,
		MaxLifetime:  time.Hour,
	}
	
	// Create database connection
	db, err := database.NewPostgresDB(dbConfig, m.logger, nil)
	if err != nil {
		return fmt.Errorf("failed to connect to registry database: %w", err)
	}
	
	m.registryDB = db
	
	// Run auto-migration for registry tables
	if err := m.autoMigrateRegistryTables(); err != nil {
		return fmt.Errorf("failed to migrate registry tables: %w", err)
	}
	
	return nil
}

// autoMigrateRegistryTables runs auto-migration for registry tables
func (m *Manager) autoMigrateRegistryTables() error {
	models := []interface{}{
		&ServiceInstance{},
		&ServiceEndpoint{},
		&ServiceHealth{},
	}
	
	for _, model := range models {
		if err := m.registryDB.AutoMigrate(model); err != nil {
			return fmt.Errorf("failed to migrate table for %T: %w", model, err)
		}
	}
	
	m.logger.Info("Registry database tables migrated successfully")
	return nil
}

// cleanupLoop periodically cleans up expired services
func (m *Manager) cleanupLoop() {
	defer m.wg.Done()
	
	ticker := time.NewTicker(m.config.ServiceRegistry.CleanupInterval)
	defer ticker.Stop()
	
	for {
		select {
		case <-m.ctx.Done():
			return
		case <-ticker.C:
			ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
			if err := m.repository.CleanupExpiredServices(ctx, m.config.ServiceRegistry.HealthCheckTTL); err != nil {
				m.logger.WithError(err).Error("Failed to cleanup expired services")
			}
			cancel()
		}
	}
}

// IsRegistryEnabled returns true if service registry is enabled
func (m *Manager) IsRegistryEnabled() bool {
	return m.config.ServiceRegistry.Enabled
}

// IsDiscoveryEnabled returns true if service discovery is enabled
func (m *Manager) IsDiscoveryEnabled() bool {
	return m.config.ServiceDiscovery.Enabled
}
