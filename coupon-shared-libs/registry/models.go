package registry

import (
	"fmt"
	"time"

	"gorm.io/gorm"
)

// ServiceInstance represents a registered service instance in the registry
type ServiceInstance struct {
	ID          string `gorm:"primaryKey;type:varchar(255)" json:"id"`
	ServiceName string `gorm:"type:varchar(100);not null;index" json:"service_name"`
	Version     string `gorm:"type:varchar(50);not null" json:"version"`
	Environment string `gorm:"type:varchar(50);not null;index" json:"environment"`
	Host        string `gorm:"type:varchar(255);not null" json:"host"`
	Port        int    `gorm:"not null" json:"port"`
	GRPCPort    int    `gorm:"not null" json:"grpc_port"`
	HTTPPort    int    `gorm:"not null" json:"http_port"`
	Status      string `gorm:"type:varchar(20);not null;index;default:'STARTING'" json:"status"`
	Metadata    string `gorm:"type:text" json:"metadata,omitempty"`
	Tags        string `gorm:"type:text" json:"tags,omitempty"`

	// Health and lifecycle
	HealthStatus   string     `gorm:"type:varchar(20);not null;default:'UNKNOWN'" json:"health_status"`
	LastHeartbeat  time.Time  `gorm:"not null" json:"last_heartbeat"`
	RegisteredAt   time.Time  `gorm:"not null" json:"registered_at"`
	DeregisteredAt *time.Time `gorm:"index" json:"deregistered_at,omitempty"`

	// Authentication
	ClientID  string `gorm:"type:varchar(100);not null" json:"client_id"`
	ClientKey string `gorm:"type:varchar(255);not null" json:"-"` // Hidden in JSON

	// GORM timestamps
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`
}

// ServiceEndpoint represents available endpoints for a service instance
type ServiceEndpoint struct {
	ID          uint   `gorm:"primaryKey" json:"id"`
	ServiceID   string `gorm:"type:varchar(255);not null;index" json:"service_id"`
	Name        string `gorm:"type:varchar(100);not null" json:"name"`
	Path        string `gorm:"type:varchar(255);not null" json:"path"`
	Method      string `gorm:"type:varchar(20);not null" json:"method"`
	Protocol    string `gorm:"type:varchar(20);not null" json:"protocol"`
	IsPublic    bool   `gorm:"not null;default:false" json:"is_public"`
	Description string `gorm:"type:text" json:"description,omitempty"`

	// Relationship
	ServiceInstance ServiceInstance `gorm:"foreignKey:ServiceID;references:ID" json:"-"`

	// GORM timestamps
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`
}

// ServiceHealth represents health check results for a service instance
type ServiceHealth struct {
	ID        uint      `gorm:"primaryKey" json:"id"`
	ServiceID string    `gorm:"type:varchar(255);not null;index" json:"service_id"`
	CheckName string    `gorm:"type:varchar(100);not null" json:"check_name"`
	Status    string    `gorm:"type:varchar(20);not null" json:"status"`
	Message   string    `gorm:"type:text" json:"message,omitempty"`
	CheckedAt time.Time `gorm:"not null" json:"checked_at"`
	Duration  int64     `gorm:"not null" json:"duration_ms"`

	// Relationship
	ServiceInstance ServiceInstance `gorm:"foreignKey:ServiceID;references:ID" json:"-"`

	// GORM timestamps
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`
}

// ServiceStatus constants
const (
	ServiceStatusStarting     = "STARTING"
	ServiceStatusHealthy      = "HEALTHY"
	ServiceStatusUnhealthy    = "UNHEALTHY"
	ServiceStatusMaintenance  = "MAINTENANCE"
	ServiceStatusDeregistered = "DEREGISTERED"
)

// HealthStatus constants
const (
	HealthStatusHealthy   = "HEALTHY"
	HealthStatusUnhealthy = "UNHEALTHY"
	HealthStatusUnknown   = "UNKNOWN"
)

// Protocol constants
const (
	ProtocolHTTP = "HTTP"
	ProtocolGRPC = "GRPC"
)

// TableName returns the table name for ServiceInstance
func (ServiceInstance) TableName() string {
	return "service_instances"
}

// TableName returns the table name for ServiceEndpoint
func (ServiceEndpoint) TableName() string {
	return "service_endpoints"
}

// TableName returns the table name for ServiceHealth
func (ServiceHealth) TableName() string {
	return "service_health"
}

// IsHealthy returns true if the service instance is healthy
func (s *ServiceInstance) IsHealthy() bool {
	return s.Status == ServiceStatusHealthy && s.HealthStatus == HealthStatusHealthy
}

// IsExpired returns true if the service instance hasn't sent heartbeat within the given duration
func (s *ServiceInstance) IsExpired(ttl time.Duration) bool {
	return time.Since(s.LastHeartbeat) > ttl
}

// GetGRPCAddress returns the gRPC address for the service instance
func (s *ServiceInstance) GetGRPCAddress() string {
	return fmt.Sprintf("%s:%d", s.Host, s.GRPCPort)
}

// GetHTTPAddress returns the HTTP address for the service instance
func (s *ServiceInstance) GetHTTPAddress() string {
	return fmt.Sprintf("%s:%d", s.Host, s.HTTPPort)
}
