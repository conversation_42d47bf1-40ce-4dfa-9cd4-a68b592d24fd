package registry

import (
	"context"
	"fmt"
	"sync"
	"time"

	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
)

// RegistrarConfig holds configuration for service registration
type RegistrarConfig struct {
	ServiceName     string        `mapstructure:"service_name"`
	ServiceVersion  string        `mapstructure:"service_version"`
	Environment     string        `mapstructure:"environment"`
	Host            string        `mapstructure:"host"`
	HTTPPort        int           `mapstructure:"http_port"`
	GRPCPort        int           `mapstructure:"grpc_port"`
	ClientID        string        `mapstructure:"client_id"`
	ClientKey       string        `mapstructure:"client_key"`
	HeartbeatInterval time.Duration `mapstructure:"heartbeat_interval"`
	HealthCheckTTL    time.Duration `mapstructure:"health_check_ttl"`
	Tags            []string      `mapstructure:"tags"`
	Metadata        map[string]string `mapstructure:"metadata"`
}

// ServiceRegistrar handles service registration and lifecycle management
type ServiceRegistrar struct {
	config     *RegistrarConfig
	repository Repository
	logger     *logging.Logger
	
	serviceID     string
	instance      *ServiceInstance
	endpoints     []*ServiceEndpoint
	
	// Lifecycle management
	ctx           context.Context
	cancel        context.CancelFunc
	wg            sync.WaitGroup
	registered    bool
	mu            sync.RWMutex
}

// NewServiceRegistrar creates a new service registrar
func NewServiceRegistrar(cfg *RegistrarConfig, repo Repository, logger *logging.Logger) *ServiceRegistrar {
	ctx, cancel := context.WithCancel(context.Background())
	
	serviceID := fmt.Sprintf("%s-%s-%s-%d", 
		cfg.ServiceName, cfg.Environment, cfg.Host, cfg.GRPCPort)
	
	return &ServiceRegistrar{
		config:     cfg,
		repository: repo,
		logger:     logger,
		serviceID:  serviceID,
		ctx:        ctx,
		cancel:     cancel,
	}
}

// NewServiceRegistrarFromConfig creates a service registrar from shared config
func NewServiceRegistrarFromConfig(cfg *config.Config, repo Repository, logger *logging.Logger) *ServiceRegistrar {
	registrarConfig := &RegistrarConfig{
		ServiceName:       cfg.Service.Name,
		ServiceVersion:    cfg.Service.Version,
		Environment:       cfg.Service.Environment,
		Host:              cfg.GRPC.Host,
		HTTPPort:          cfg.Service.Port,
		GRPCPort:          cfg.Service.GRPCPort,
		ClientID:          cfg.Service.ClientID,
		ClientKey:         cfg.Service.ClientKey,
		HeartbeatInterval: 30 * time.Second, // Default values
		HealthCheckTTL:    90 * time.Second,
		Tags:              []string{},
		Metadata:          make(map[string]string),
	}
	
	return NewServiceRegistrar(registrarConfig, repo, logger)
}

// Register registers the service instance
func (r *ServiceRegistrar) Register(ctx context.Context) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	if r.registered {
		return fmt.Errorf("service already registered")
	}
	
	// Create service instance
	r.instance = &ServiceInstance{
		ID:            r.serviceID,
		ServiceName:   r.config.ServiceName,
		Version:       r.config.ServiceVersion,
		Environment:   r.config.Environment,
		Host:          r.config.Host,
		Port:          r.config.HTTPPort,
		GRPCPort:      r.config.GRPCPort,
		HTTPPort:      r.config.HTTPPort,
		Status:        ServiceStatusStarting,
		HealthStatus:  HealthStatusUnknown,
		ClientID:      r.config.ClientID,
		ClientKey:     r.config.ClientKey,
		Tags:          r.formatTags(),
		Metadata:      r.formatMetadata(),
	}
	
	// Register with repository
	if err := r.repository.RegisterService(ctx, r.instance); err != nil {
		return fmt.Errorf("failed to register service: %w", err)
	}
	
	// Register endpoints if any
	if len(r.endpoints) > 0 {
		if err := r.repository.RegisterEndpoints(ctx, r.serviceID, r.endpoints); err != nil {
			r.logger.WithContext(ctx).WithError(err).Warn("Failed to register endpoints")
		}
	}
	
	r.registered = true
	
	// Start heartbeat goroutine
	r.wg.Add(1)
	go r.heartbeatLoop()
	
	r.logger.WithContext(ctx).WithFields(logging.Fields{
		"service_id":   r.serviceID,
		"service_name": r.config.ServiceName,
		"grpc_address": r.instance.GetGRPCAddress(),
		"http_address": r.instance.GetHTTPAddress(),
	}).Info("Service registered successfully")
	
	return nil
}

// AddEndpoint adds an endpoint to be registered with the service
func (r *ServiceRegistrar) AddEndpoint(name, path, method, protocol string, isPublic bool, description string) {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	endpoint := &ServiceEndpoint{
		Name:        name,
		Path:        path,
		Method:      method,
		Protocol:    protocol,
		IsPublic:    isPublic,
		Description: description,
	}
	
	r.endpoints = append(r.endpoints, endpoint)
}

// UpdateStatus updates the service status
func (r *ServiceRegistrar) UpdateStatus(ctx context.Context, status string) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	if !r.registered {
		return fmt.Errorf("service not registered")
	}
	
	r.instance.Status = status
	
	// Update in database would require additional repository method
	// For now, this will be updated on next heartbeat
	
	r.logger.WithContext(ctx).WithFields(logging.Fields{
		"service_id": r.serviceID,
		"status":     status,
	}).Info("Service status updated")
	
	return nil
}

// MarkHealthy marks the service as healthy
func (r *ServiceRegistrar) MarkHealthy(ctx context.Context) error {
	return r.UpdateStatus(ctx, ServiceStatusHealthy)
}

// MarkUnhealthy marks the service as unhealthy
func (r *ServiceRegistrar) MarkUnhealthy(ctx context.Context) error {
	return r.UpdateStatus(ctx, ServiceStatusUnhealthy)
}

// Deregister gracefully deregisters the service
func (r *ServiceRegistrar) Deregister(ctx context.Context) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	if !r.registered {
		return nil // Already deregistered
	}
	
	// Stop heartbeat loop
	r.cancel()
	r.wg.Wait()
	
	// Deregister from repository
	if err := r.repository.DeregisterService(ctx, r.serviceID); err != nil {
		r.logger.WithContext(ctx).WithError(err).Error("Failed to deregister service")
		return fmt.Errorf("failed to deregister service: %w", err)
	}
	
	r.registered = false
	
	r.logger.WithContext(ctx).WithFields(logging.Fields{
		"service_id": r.serviceID,
	}).Info("Service deregistered successfully")
	
	return nil
}

// GetServiceID returns the service ID
func (r *ServiceRegistrar) GetServiceID() string {
	return r.serviceID
}

// GetInstance returns the service instance
func (r *ServiceRegistrar) GetInstance() *ServiceInstance {
	r.mu.RLock()
	defer r.mu.RUnlock()
	return r.instance
}

// IsRegistered returns true if the service is registered
func (r *ServiceRegistrar) IsRegistered() bool {
	r.mu.RLock()
	defer r.mu.RUnlock()
	return r.registered
}

// heartbeatLoop sends periodic heartbeats to maintain service registration
func (r *ServiceRegistrar) heartbeatLoop() {
	defer r.wg.Done()
	
	ticker := time.NewTicker(r.config.HeartbeatInterval)
	defer ticker.Stop()
	
	for {
		select {
		case <-r.ctx.Done():
			return
		case <-ticker.C:
			if err := r.sendHeartbeat(); err != nil {
				r.logger.WithError(err).Error("Failed to send heartbeat")
			}
		}
	}
}

// sendHeartbeat sends a heartbeat to the registry
func (r *ServiceRegistrar) sendHeartbeat() error {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	
	if err := r.repository.UpdateHeartbeat(ctx, r.serviceID); err != nil {
		return fmt.Errorf("failed to update heartbeat: %w", err)
	}
	
	r.logger.WithFields(logging.Fields{
		"service_id": r.serviceID,
	}).Debug("Heartbeat sent successfully")
	
	return nil
}

// formatTags converts tags slice to string for storage
func (r *ServiceRegistrar) formatTags() string {
	if len(r.config.Tags) == 0 {
		return ""
	}
	
	result := ""
	for i, tag := range r.config.Tags {
		if i > 0 {
			result += ","
		}
		result += tag
	}
	return result
}

// formatMetadata converts metadata map to string for storage
func (r *ServiceRegistrar) formatMetadata() string {
	if len(r.config.Metadata) == 0 {
		return ""
	}
	
	result := ""
	first := true
	for key, value := range r.config.Metadata {
		if !first {
			result += ","
		}
		result += fmt.Sprintf("%s=%s", key, value)
		first = false
	}
	return result
}
