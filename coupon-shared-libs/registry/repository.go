package registry

import (
	"context"
	"fmt"
	"time"

	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gorm.io/gorm"
)

// Repository defines the interface for service registry database operations
type Repository interface {
	// Service Instance operations
	RegisterService(ctx context.Context, instance *ServiceInstance) error
	UpdateHeartbeat(ctx context.Context, serviceID string) error
	DeregisterService(ctx context.Context, serviceID string) error
	GetService(ctx context.Context, serviceID string) (*ServiceInstance, error)
	GetServicesByName(ctx context.Context, serviceName string) ([]*ServiceInstance, error)
	GetHealthyServices(ctx context.Context, serviceName string) ([]*ServiceInstance, error)
	GetAllServices(ctx context.Context) ([]*ServiceInstance, error)
	CleanupExpiredServices(ctx context.Context, ttl time.Duration) error

	// Service Endpoint operations
	RegisterEndpoints(ctx context.Context, serviceID string, endpoints []*ServiceEndpoint) error
	GetServiceEndpoints(ctx context.Context, serviceID string) ([]*ServiceEndpoint, error)

	// Health operations
	RecordHealthCheck(ctx context.Context, health *ServiceHealth) error
	GetServiceHealth(ctx context.Context, serviceID string) ([]*ServiceHealth, error)
	GetLatestHealthCheck(ctx context.Context, serviceID, checkName string) (*ServiceHealth, error)
}

// repository implements the Repository interface
type repository struct {
	db     *gorm.DB
	logger *logging.Logger
}

// NewRepository creates a new service registry repository with database connection
func NewRepository(db *gorm.DB, logger *logging.Logger) Repository {
	return &repository{
		db:     db,
		logger: logger,
	}
}

// RegisterService registers a new service instance
func (r *repository) RegisterService(ctx context.Context, instance *ServiceInstance) error {
	instance.RegisteredAt = time.Now()
	instance.LastHeartbeat = time.Now()

	if err := r.db.WithContext(ctx).Create(instance).Error; err != nil {
		r.logger.WithContext(ctx).WithError(err).Error("Failed to register service")
		return fmt.Errorf("failed to register service: %w", err)
	}

	r.logger.WithFields(logging.Fields{
		"service_id":   instance.ID,
		"service_name": instance.ServiceName,
		"host":         instance.Host,
		"grpc_port":    instance.GRPCPort,
	}).Info("Service registered successfully")

	return nil
}

// UpdateHeartbeat updates the last heartbeat timestamp for a service
func (r *repository) UpdateHeartbeat(ctx context.Context, serviceID string) error {
	result := r.db.WithContext(ctx).
		Model(&ServiceInstance{}).
		Where("id = ? AND deleted_at IS NULL", serviceID).
		Update("last_heartbeat", time.Now())

	if result.Error != nil {
		r.logger.WithContext(ctx).WithError(result.Error).Error("Failed to update heartbeat")
		return fmt.Errorf("failed to update heartbeat: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("service not found: %s", serviceID)
	}

	return nil
}

// DeregisterService marks a service as deregistered
func (r *repository) DeregisterService(ctx context.Context, serviceID string) error {
	now := time.Now()
	result := r.db.WithContext(ctx).
		Model(&ServiceInstance{}).
		Where("id = ? AND deleted_at IS NULL", serviceID).
		Updates(map[string]interface{}{
			"status":          ServiceStatusDeregistered,
			"deregistered_at": &now,
		})

	if result.Error != nil {
		r.logger.WithContext(ctx).WithError(result.Error).Error("Failed to deregister service")
		return fmt.Errorf("failed to deregister service: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("service not found: %s", serviceID)
	}

	r.logger.WithFields(logging.Fields{
		"service_id": serviceID,
	}).Info("Service deregistered successfully")

	return nil
}

// GetService retrieves a service instance by ID
func (r *repository) GetService(ctx context.Context, serviceID string) (*ServiceInstance, error) {
	var instance ServiceInstance
	if err := r.db.WithContext(ctx).
		Where("id = ? AND deleted_at IS NULL", serviceID).
		First(&instance).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("service not found: %s", serviceID)
		}
		return nil, fmt.Errorf("failed to get service: %w", err)
	}

	return &instance, nil
}

// GetServicesByName retrieves all instances of a service by name
func (r *repository) GetServicesByName(ctx context.Context, serviceName string) ([]*ServiceInstance, error) {
	var instances []*ServiceInstance
	if err := r.db.WithContext(ctx).
		Where("service_name = ? AND deleted_at IS NULL", serviceName).
		Order("last_heartbeat DESC").
		Find(&instances).Error; err != nil {
		return nil, fmt.Errorf("failed to get services by name: %w", err)
	}

	return instances, nil
}

// GetHealthyServices retrieves healthy instances of a service by name
func (r *repository) GetHealthyServices(ctx context.Context, serviceName string) ([]*ServiceInstance, error) {
	var instances []*ServiceInstance
	if err := r.db.WithContext(ctx).
		Where("service_name = ? AND status = ? AND health_status = ? AND deleted_at IS NULL",
			serviceName, ServiceStatusHealthy, HealthStatusHealthy).
		Order("last_heartbeat DESC").
		Find(&instances).Error; err != nil {
		return nil, fmt.Errorf("failed to get healthy services: %w", err)
	}

	return instances, nil
}

// GetAllServices retrieves all registered services
func (r *repository) GetAllServices(ctx context.Context) ([]*ServiceInstance, error) {
	var instances []*ServiceInstance
	if err := r.db.WithContext(ctx).
		Where("deleted_at IS NULL").
		Order("service_name, last_heartbeat DESC").
		Find(&instances).Error; err != nil {
		return nil, fmt.Errorf("failed to get all services: %w", err)
	}

	return instances, nil
}

// CleanupExpiredServices removes services that haven't sent heartbeat within TTL
func (r *repository) CleanupExpiredServices(ctx context.Context, ttl time.Duration) error {
	expiredTime := time.Now().Add(-ttl)

	result := r.db.WithContext(ctx).
		Model(&ServiceInstance{}).
		Where("last_heartbeat < ? AND status != ? AND deleted_at IS NULL",
			expiredTime, ServiceStatusDeregistered).
		Updates(map[string]interface{}{
			"status":          ServiceStatusDeregistered,
			"deregistered_at": time.Now(),
		})

	if result.Error != nil {
		r.logger.WithContext(ctx).WithError(result.Error).Error("Failed to cleanup expired services")
		return fmt.Errorf("failed to cleanup expired services: %w", result.Error)
	}

	if result.RowsAffected > 0 {
		r.logger.WithFields(logging.Fields{
			"expired_count": result.RowsAffected,
			"ttl":           ttl.String(),
		}).Info("Cleaned up expired services")
	}

	return nil
}

// RegisterEndpoints registers endpoints for a service
func (r *repository) RegisterEndpoints(ctx context.Context, serviceID string, endpoints []*ServiceEndpoint) error {
	// First, delete existing endpoints for this service
	if err := r.db.WithContext(ctx).
		Where("service_id = ?", serviceID).
		Delete(&ServiceEndpoint{}).Error; err != nil {
		return fmt.Errorf("failed to delete existing endpoints: %w", err)
	}

	// Then create new endpoints
	for _, endpoint := range endpoints {
		endpoint.ServiceID = serviceID
		if err := r.db.WithContext(ctx).Create(endpoint).Error; err != nil {
			return fmt.Errorf("failed to register endpoint: %w", err)
		}
	}

	r.logger.WithFields(logging.Fields{
		"service_id":     serviceID,
		"endpoint_count": len(endpoints),
	}).Info("Service endpoints registered")

	return nil
}

// GetServiceEndpoints retrieves endpoints for a service
func (r *repository) GetServiceEndpoints(ctx context.Context, serviceID string) ([]*ServiceEndpoint, error) {
	var endpoints []*ServiceEndpoint
	if err := r.db.WithContext(ctx).
		Where("service_id = ? AND deleted_at IS NULL", serviceID).
		Find(&endpoints).Error; err != nil {
		return nil, fmt.Errorf("failed to get service endpoints: %w", err)
	}

	return endpoints, nil
}

// RecordHealthCheck records a health check result
func (r *repository) RecordHealthCheck(ctx context.Context, health *ServiceHealth) error {
	health.CheckedAt = time.Now()

	if err := r.db.WithContext(ctx).Create(health).Error; err != nil {
		return fmt.Errorf("failed to record health check: %w", err)
	}

	return nil
}

// GetServiceHealth retrieves health check history for a service
func (r *repository) GetServiceHealth(ctx context.Context, serviceID string) ([]*ServiceHealth, error) {
	var healthChecks []*ServiceHealth
	if err := r.db.WithContext(ctx).
		Where("service_id = ? AND deleted_at IS NULL", serviceID).
		Order("checked_at DESC").
		Limit(100). // Limit to last 100 health checks
		Find(&healthChecks).Error; err != nil {
		return nil, fmt.Errorf("failed to get service health: %w", err)
	}

	return healthChecks, nil
}

// GetLatestHealthCheck retrieves the latest health check for a specific check name
func (r *repository) GetLatestHealthCheck(ctx context.Context, serviceID, checkName string) (*ServiceHealth, error) {
	var health ServiceHealth
	if err := r.db.WithContext(ctx).
		Where("service_id = ? AND check_name = ? AND deleted_at IS NULL", serviceID, checkName).
		Order("checked_at DESC").
		First(&health).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil // No health check found
		}
		return nil, fmt.Errorf("failed to get latest health check: %w", err)
	}

	return &health, nil
}
