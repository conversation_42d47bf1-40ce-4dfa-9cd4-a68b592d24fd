package middleware

import (
	"context"
	"strings"

	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/auth"
	"google.golang.org/grpc/metadata"
)

// PublicMethods defines which methods don't require authentication
var PublicMethods = map[string]bool{
	"/user.v1.UserService/CreateUser":  true,
	"/user.v1.UserService/HealthCheck": true,
}

// ServiceToServiceMethods defines which methods support service-to-service authentication
var ServiceToServiceMethods = map[string]bool{
	"/user.v1.UserService/GetUserByEmail": true,
	"/user.v1.UserService/GetUser":        true,
}

// CreateSelectiveAuthFunc creates an auth function that skips authentication for public methods
// and supports service-to-service authentication for specific methods
func CreateSelectiveAuthFunc(jwtManager *auth.JWTManager) func(context.Context) (context.Context, error) {
	return func(ctx context.Context) (context.Context, error) {
		// Get the method name from the context
		md, ok := metadata.FromIncomingContext(ctx)
		if !ok {
			return nil, nil // Let it pass, will be handled by the service layer
		}

		// Check if this is a public method by looking at the method in metadata
		method := getMethodFromMetadata(md)
		if PublicMethods[method] {
			return ctx, nil // Skip authentication for public methods
		}

		// Check if this is a service-to-service method
		if ServiceToServiceMethods[method] {
			// Check for service credentials first
			clientIDs := md.Get("client-id")
			clientKeys := md.Get("client-key")
			if len(clientIDs) > 0 && len(clientKeys) > 0 {
				// This is a service-to-service call, let it pass
				// The actual validation will be done by the auth service
				return ctx, nil
			}
		}

		// For non-public methods without service credentials, apply JWT authentication
		return jwtManager.AuthFunc(ctx)
	}
}

// Helper function to extract method name from metadata
func getMethodFromMetadata(md metadata.MD) string {
	// This is a simplified implementation
	// In practice, you might need to extract this differently
	if methods := md.Get(":path"); len(methods) > 0 {
		return methods[0]
	}
	return ""
}

// IsPublicMethod checks if a method is public
func IsPublicMethod(fullMethod string) bool {
	// Extract just the method part from the full method path
	parts := strings.Split(fullMethod, "/")
	if len(parts) >= 3 {
		method := "/" + parts[1] + "/" + parts[2]
		return PublicMethods[method]
	}
	return PublicMethods[fullMethod]
}
