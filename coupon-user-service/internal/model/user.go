package model

import (
	"time"

	"github.com/google/uuid"
)

// UserType represents the type of user
type UserType string

const (
	UserTypeNew UserType = "NEW"
	UserTypeVIP UserType = "VIP"
)

// User represents user profile data only
// Roles and authentication data are managed by auth-service
type User struct {
	ID        string    `gorm:"type:uuid;primary_key;"`
	Name      string    `gorm:"type:varchar(255);not null"`
	Email     string    `gorm:"type:varchar(255);not null;uniqueIndex"`
	Type      UserType  `gorm:"type:varchar(10);not null;default:'NEW'"`
	CreatedAt time.Time `gorm:"not null"`
	UpdatedAt time.Time `gorm:"not null"`
}

func (User) TableName() string { return "users" }

func NewUser(name, email string) *User {
	return &User{
		ID:    uuid.NewString(),
		Name:  name,
		Email: email,
		Type:  UserTypeNew, // Default to NEW user type
	}
}
