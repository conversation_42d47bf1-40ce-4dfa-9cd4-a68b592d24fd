# gRPC Convention Fixes

## Overview
This document summarizes the changes made to align the user-service with the auth-service conventions for gRPC server setup, removing direct middleware imports and using the shared gRPC library.

## Changes Made

### ✅ **User Service (`coupon-user-service/cmd/server/main.go`)**

**Before (Non-standard approach):**
```go
import (
    grpc_recovery "github.com/grpc-ecosystem/go-grpc-middleware/recovery"
    grpc_ctxtags "github.com/grpc-ecosystem/go-grpc-middleware/tags"
    grpc_opentracing "github.com/grpc-ecosystem/go-grpc-middleware/tracing/opentracing"
    "google.golang.org/grpc"
)

// Manual gRPC server setup
grpcServer := grpc.NewServer(
    grpc.ChainUnaryInterceptor(
        grpc_ctxtags.UnaryServerInterceptor(),
        grpc_opentracing.UnaryServerInterceptor(),
        grpc_recovery.UnaryServerInterceptor(),
        authInterceptor,
    ),
)
```

**After (Following auth-service convention):**
```go
import (
    shared_grpc "gitlab.zalopay.vn/phunn4/coupon-shared-libs/grpc"
)

// Using shared gRPC library
grpcServerWrapper := shared_grpc.NewServer(&cfg.GRPC, logger, metrics, authFunc)
```

### ✅ **Updated Middleware (`coupon-user-service/internal/middleware/grpc_auth.go`)**

**Before (Custom interceptor):**
```go
func AuthInterceptor(jwtAuthFunc grpc_auth.AuthFunc) grpc.UnaryServerInterceptor {
    // Complex interceptor logic
}
```

**After (Selective auth function):**
```go
func CreateSelectiveAuthFunc(jwtManager *auth.JWTManager) func(context.Context) (context.Context, error) {
    return func(ctx context.Context) (context.Context, error) {
        // Check if method is public
        if PublicMethods[method] {
            return ctx, nil // Skip authentication
        }
        return jwtManager.AuthFunc(ctx) // Apply JWT auth
    }
}
```

### ✅ **Removed Direct Dependencies**

**Removed imports:**
- `github.com/grpc-ecosystem/go-grpc-middleware/recovery`
- `github.com/grpc-ecosystem/go-grpc-middleware/tags`
- `github.com/grpc-ecosystem/go-grpc-middleware/tracing/opentracing`
- `google.golang.org/grpc` (direct usage)
- `net` (no longer needed for manual listener setup)

**Added imports:**
- `shared_grpc "gitlab.zalopay.vn/phunn4/coupon-shared-libs/grpc"`

## Benefits of the Changes

### 🎯 **Consistency**
- All gRPC services now follow the same pattern
- Standardized server setup across the codebase
- Consistent middleware application

### 🔧 **Maintainability**
- Centralized gRPC configuration in shared library
- Easier to update middleware across all services
- Reduced code duplication

### 📦 **Dependency Management**
- Fewer direct dependencies on gRPC middleware packages
- Dependencies managed through shared library
- Cleaner go.mod files

### 🛡️ **Security**
- Consistent authentication handling
- Proper public method handling
- Standardized error responses

## Service Comparison

| Aspect | Auth Service | User Service (Before) | User Service (After) |
|--------|-------------|----------------------|---------------------|
| gRPC Setup | `shared_grpc.NewServer()` | Manual `grpc.NewServer()` | `shared_grpc.NewServer()` ✅ |
| Middleware | Shared library | Direct imports | Shared library ✅ |
| Auth Handling | Service-layer auth | Custom interceptor | Selective auth func ✅ |
| Dependencies | Minimal | Many direct imports | Minimal ✅ |

## Public Methods Handling

Both services now properly handle public vs protected methods:

**Auth Service:**
- `Login` - Public (no auth required)
- `HealthCheck` - Public
- All other methods - Protected (require service credentials)

**User Service:**
- `CreateUser` - Public (for registration)
- `HealthCheck` - Public
- All other methods - Protected (require JWT tokens)

## Configuration

Both services use the same gRPC configuration structure:

```yaml
grpc:
  host: "0.0.0.0"
  port: 50051
  keepalive_time: "30s"
  keepalive_timeout: "5s"
  max_receive_size: 4194304
  max_send_size: 4194304
```

## Testing

To verify the changes work correctly:

1. **Start both services:**
   ```bash
   # Terminal 1
   cd coupon-auth-service && go run cmd/server/main.go
   
   # Terminal 2  
   cd coupon-user-service && go run cmd/server/main.go
   ```

2. **Test public methods (should work without auth):**
   ```bash
   # Auth service login
   grpcurl -plaintext -d '{"email":"<EMAIL>","password":"password"}' \
     localhost:50051 auth.v1.AuthService/Login
   
   # User service registration
   grpcurl -plaintext -d '{"name":"Test","email":"<EMAIL>","password":"password"}' \
     localhost:50052 user.v1.UserService/CreateUser
   ```

3. **Test protected methods (should require auth):**
   ```bash
   # Should fail without JWT token
   grpcurl -plaintext -d '{"user_id":"123"}' \
     localhost:50052 user.v1.UserService/GetUser
   ```

## Next Steps

- ✅ User service now follows auth-service conventions
- ✅ All gRPC services use shared library
- ✅ Consistent authentication handling
- ✅ Clean dependency management

The codebase now has consistent gRPC server setup across all services, making it easier to maintain and extend.
