#!/bin/bash

# Database Studio Access Script
echo "🗄️  Coupon Microservice Database Studio"
echo "======================================"
echo ""

# Check if shared Adminer is running
if ! docker ps | grep -q "shared-adminer"; then
    echo "⚠️  Shared Adminer is not running. Starting services..."
    echo ""

    # Start auth service (which includes shared Adminer)
    echo "🚀 Starting auth service with shared Adminer..."
    cd coupon-auth-service && docker-compose up -d
    cd ..

    # Start user service
    echo "🚀 Starting user service..."
    cd coupon-user-service && docker-compose up -d
    cd ..

    echo "⏳ Waiting for shared Adminer to be ready..."
    sleep 5
fi

echo "✅ Shared Adminer is running!"
echo ""
echo "📊 Shared Database Studio Access Information:"
echo "============================================="
echo "🌐 Web Interface: http://localhost:8080"
echo "   (Use this single interface to connect to both databases)"
echo ""
echo "🔐 Database Connections (use same login form):"
echo ""
echo "📁 Auth Service Database:"
echo "   System: PostgreSQL"
echo "   Server: postgres-auth"
echo "   Username: coupon"
echo "   Password: coupon"
echo "   Database: auth_db"
echo ""
echo "👤 User Service Database:"
echo "   System: PostgreSQL"
echo "   Server: postgres-user"
echo "   Username: coupon"
echo "   Password: coupon"
echo "   Database: user_db"
echo ""
echo "💡 How to switch databases:"
echo "   1. Login to one database"
echo "   2. Click 'Logout' to switch to another database"
echo "   3. Enter different server name (postgres-auth or postgres-user)"
echo ""
echo "💡 Quick SQL Queries to Try:"
echo "   Auth DB: SELECT * FROM roles;"
echo "   Auth DB: SELECT * FROM user_credentials;"
echo "   User DB: SELECT * FROM users;"
echo ""

# Try to open browser automatically
if command -v open >/dev/null 2>&1; then
    echo "🌐 Opening browser..."
    open http://localhost:8080
elif command -v xdg-open >/dev/null 2>&1; then
    echo "🌐 Opening browser..."
    xdg-open http://localhost:8080
elif command -v start >/dev/null 2>&1; then
    echo "🌐 Opening browser..."
    start http://localhost:8080
else
    echo "🌐 Please open http://localhost:8080 in your browser"
fi

echo ""
echo "🎯 Happy database exploring!"
